<?php
/**
 * Targeted Database Cleanup Script
 * Handles foreign key constraints and view issues
 */

// Database credentials
define('DB_SERVER', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', '');
define('DB_NAME', 'maxcel_elearning');

// Create connection
try {
    $pdo = new PDO("mysql:host=" . DB_SERVER . ";dbname=" . DB_NAME, DB_USERNAME, DB_PASSWORD);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<h1>Targeted Database Cleanup Script</h1>";
    echo "<p>Connected to database successfully.</p>";
} catch(PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

// Function to execute SQL with error handling
function executeSQL($sql, $description) {
    global $pdo;
    try {
        $pdo->exec($sql);
        echo "<p style='color: green;'>✓ $description - SUCCESS</p>";
        return true;
    } catch(PDOException $e) {
        echo "<p style='color: red;'>✗ $description - ERROR: " . $e->getMessage() . "</p>";
        return false;
    }
}

// Function to check if table exists
function tableExists($tableName) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$tableName]);
        return $stmt->rowCount() > 0;
    } catch(PDOException $e) {
        return false;
    }
}

// Function to check if it's a view
function isView($tableName) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            SELECT TABLE_TYPE 
            FROM information_schema.TABLES 
            WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?
        ");
        $stmt->execute([DB_NAME, $tableName]);
        $result = $stmt->fetch();
        return $result && $result['TABLE_TYPE'] === 'VIEW';
    } catch(PDOException $e) {
        return false;
    }
}

echo "<h2>Starting Targeted Cleanup...</h2>";

// 1. Handle foreign key constraints for table removal
echo "<h3>1. Removing Foreign Key Constraints</h3>";

// Disable foreign key checks temporarily
executeSQL("SET FOREIGN_KEY_CHECKS = 0", "Disable foreign key checks");

// 2. Remove problematic tables with foreign key constraints
echo "<h3>2. Removing Problematic Tables</h3>";

$tablesToRemove = ['quizzes', 'modules', 'lessons', 'forums'];

foreach ($tablesToRemove as $table) {
    if (tableExists($table)) {
        executeSQL("DROP TABLE IF EXISTS `$table`", "Remove '$table' table");
    } else {
        echo "<p style='color: blue;'>ℹ '$table' table doesn't exist - skipping</p>";
    }
}

// 3. Handle submissions table (if it's a view, recreate as table)
echo "<h3>3. Fixing Submissions Table</h3>";

if (tableExists('submissions')) {
    if (isView('submissions')) {
        echo "<p style='color: orange;'>⚠ 'submissions' is a view, converting to table...</p>";
        
        // Create new submissions table structure
        executeSQL("
            CREATE TABLE submissions_new (
                submission_id INT AUTO_INCREMENT PRIMARY KEY,
                activity_id INT NOT NULL,
                user_id INT NOT NULL,
                submission_content TEXT,
                submission_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_late TINYINT(1) DEFAULT 0,
                grade FLOAT DEFAULT NULL,
                feedback TEXT,
                graded_by INT DEFAULT NULL,
                graded_at TIMESTAMP NULL,
                FOREIGN KEY (activity_id) REFERENCES activities(activity_id) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
            )
        ", "Create new submissions table structure");
        
        // Copy data from view to new table (if possible)
        executeSQL("INSERT INTO submissions_new (activity_id, user_id, submission_content, submission_date, is_late, grade, feedback, graded_by, graded_at) SELECT activity_id, user_id, submission_content, submission_date, is_late, grade, feedback, graded_by, graded_at FROM submissions", "Copy data from submissions view");
        
        // Drop the view
        executeSQL("DROP VIEW submissions", "Remove submissions view");
        
        // Rename new table
        executeSQL("RENAME TABLE submissions_new TO submissions", "Rename new table to submissions");
    } else {
        echo "<p style='color: blue;'>ℹ 'submissions' is already a proper table</p>";
    }
}

// Re-enable foreign key checks
executeSQL("SET FOREIGN_KEY_CHECKS = 1", "Re-enable foreign key checks");

// 4. Final cleanup and optimization
echo "<h3>4. Final Optimizations</h3>";

// Optimize remaining tables
$tablesToOptimize = ['users', 'courses', 'activities', 'submissions', 'enrollments', 'quiz_questions', 'quiz_options', 'quiz_settings'];

foreach ($tablesToOptimize as $table) {
    if (tableExists($table)) {
        executeSQL("OPTIMIZE TABLE `$table`", "Optimize '$table' table");
    }
}

echo "<h2>Targeted Cleanup Completed!</h2>";
echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; margin: 10px 0;'>";
echo "<p><strong>Summary of targeted cleanup:</strong></p>";
echo "<ul>";
echo "<li>✓ Removed foreign key constraints temporarily</li>";
echo "<li>✓ Removed problematic tables (quizzes, modules, lessons, forums)</li>";
echo "<li>✓ Fixed submissions table structure</li>";
echo "<li>✓ Re-enabled foreign key constraints</li>";
echo "<li>✓ Optimized remaining tables</li>";
echo "</ul>";
echo "</div>";

echo "<p style='color: green; font-weight: bold; font-size: 18px;'>✓ Targeted cleanup completed successfully!</p>";
?>
