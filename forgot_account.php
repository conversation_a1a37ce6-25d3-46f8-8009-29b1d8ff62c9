<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/email_helper.php';

// Initialize variables
$username = "";
$username_err = "";
$success_msg = "";
$error_msg = "";

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Check if username is empty
    if (empty(trim($_POST["username"]))) {
        $username_err = "Please enter your username.";
    } else {
        $username = trim($_POST["username"]);
    }

    // If no errors, proceed with account recovery
    if (empty($username_err)) {
        // Check if username exists in the database
        global $pdo;
        try {
            $stmt = $pdo->prepare("SELECT user_id, username, email FROM users WHERE username = :username");
            $stmt->bindParam(':username', $username);
            $stmt->execute();

            if ($stmt->rowCount() > 0) {
                $user = $stmt->fetch();
                $userId = $user['user_id'];
                $username = $user['username'];
                $email = $user['email'];

                // Generate a random verification code
                $verificationCode = generateVerificationCode();

                // Store the verification code in the database
                $expiryTime = date('Y-m-d H:i:s', strtotime('+24 hours')); // Code expires in 24 hours

                // Check if a reset code already exists for this user
                $stmt = $pdo->prepare("SELECT * FROM password_reset WHERE user_id = :userId");
                $stmt->bindParam(':userId', $userId);
                $stmt->execute();

                if ($stmt->rowCount() > 0) {
                    // Update existing record
                    $stmt = $pdo->prepare("UPDATE password_reset SET reset_code = :code, expiry_time = :expiryTime WHERE user_id = :userId");
                } else {
                    // Insert new record
                    $stmt = $pdo->prepare("INSERT INTO password_reset (user_id, reset_code, expiry_time) VALUES (:userId, :code, :expiryTime)");
                }

                $stmt->bindParam(':userId', $userId);
                $stmt->bindParam(':code', $verificationCode);
                $stmt->bindParam(':expiryTime', $expiryTime);
                $stmt->execute();

                // Store verification code in session for development purposes
                $_SESSION['dev_verification_code'] = $verificationCode;

                // Try to send email with verification code
                $emailSent = sendVerificationCodeEmail($email, $username, $verificationCode);

                // In development mode, we'll always proceed even if email sending fails
                if ($emailSent || DEVELOPMENT_MODE) {
                    // If we're in development mode and email failed, show a warning
                    if (!$emailSent && DEVELOPMENT_MODE) {
                        // Log the issue but continue
                        error_log("Email sending failed in development mode. Proceeding anyway.");
                    }

                    $success_msg = "A verification code has been sent to your email address (" . maskEmail($email) . "). Please check your inbox.";
                    // Redirect to verification page
                    $_SESSION['recovery_email'] = $email;
                    header("location: verify_code.php");
                    exit;
                } else {
                    $error_msg = "Failed to send verification email. Please try again later.";
                }
            } else {
                // Username not found, but don't reveal this for security reasons
                $success_msg = "If your username is registered, a verification code has been sent to your email. Please check your inbox.";
                // Add a delay to prevent timing attacks
                sleep(1);
            }
        } catch (PDOException $e) {
            $error_msg = "An error occurred. Please try again later.";
            // Log the error for administrators
            error_log("Account recovery error: " . $e->getMessage());
        }
    }
}


?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forgot Account - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Google+Sans:wght@400;500;700&family=Roboto:wght@300;400;500;700&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="css/light-theme.css">
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            background-image: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('images/AdobeStock_271791778.jpeg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
        }

        .forgot-container {
            width: 448px;
            padding: 48px 40px 36px;
            background-color: rgba(255, 255, 255, 0.85);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            animation: fadeIn 0.5s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .forgot-logo {
            text-align: center;
            margin-bottom: 32px;
        }

        .forgot-logo h1 {
            color: var(--primary-color);
            font-family: 'Google Sans', sans-serif;
            font-weight: 500;
            font-size: 28px;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .forgot-logo h1 i {
            margin-right: 12px;
            font-size: 32px;
        }

        .forgot-logo p {
            color: var(--text-secondary);
            margin-top: 12px;
            font-size: 16px;
        }

        .form-group {
            margin-bottom: 24px;
            position: relative;
        }

        .form-control {
            height: 56px;
            padding: 16px;
            font-size: 16px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.25);
            transform: translateY(-2px);
        }

        .btn-submit {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 14px 24px;
            font-size: 16px;
            font-weight: 500;
            font-family: 'Google Sans', sans-serif;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            box-shadow: 0 4px 6px rgba(50, 50, 93, 0.11), 0 1px 3px rgba(0, 0, 0, 0.08);
        }

        .btn-submit:hover {
            background-color: var(--primary-hover);
            transform: translateY(-2px);
            box-shadow: 0 7px 14px rgba(50, 50, 93, 0.1), 0 3px 6px rgba(0, 0, 0, 0.08);
        }

        .btn-submit:active {
            transform: translateY(1px);
        }

        .alert {
            margin-bottom: 24px;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 4px 6px rgba(50, 50, 93, 0.11), 0 1px 3px rgba(0, 0, 0, 0.08);
        }

        .text-center a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .text-center a:hover {
            color: var(--primary-hover);
            text-decoration: underline;
        }

        .back-to-login {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            text-decoration: none;
            font-family: 'Google Sans', sans-serif;
            font-size: 16px;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
        }

        .back-to-login i {
            margin-right: 8px;
        }

        .back-to-login:hover {
            transform: translateX(-5px);
            color: rgba(255, 255, 255, 0.8);
        }
    </style>
</head>
<body>
    <a href="login.php" class="back-to-login">
        <i class="fas fa-arrow-left"></i> Back to Login
    </a>
    <div class="forgot-container">
        <div class="forgot-logo">
            <h1><i class="fas fa-graduation-cap"></i> <?php echo APP_NAME; ?></h1>
            <p>Account Recovery</p>

        </div>

        <?php
        if (!empty($error_msg)) {
            echo '<div class="alert alert-danger">' . $error_msg . '</div>';
        }

        if (!empty($success_msg)) {
            echo '<div class="alert alert-success">' . $success_msg . '</div>';
        }
        ?>

        <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
            <div class="form-group">
                <input type="text" name="username" placeholder="Enter your username" class="form-control <?php echo (!empty($username_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $username; ?>">
                <span class="invalid-feedback"><?php echo $username_err; ?></span>
            </div>
            <div class="form-group">
                <button type="submit" class="btn-submit">Send Verification Code</button>
            </div>
            <div class="text-center mt-3">
                <p>Remember your account? <a href="login.php">Sign in</a></p>
            </div>
        </form>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</body>
</html>
