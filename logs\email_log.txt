==========================================================
Email sent at: 2025-05-16 15:58:53
To: <EMAIL>
Subject: Classroom - Account Recovery
Headers: From: <EMAIL>

----------------------------------------------------------
Hello,

You have requested to recover your account at Classroom.

Your username is: maxceljane.narisma

To reset your password, use the following verification code:
587145

This code will expire in 1 hour.

If you did not request this, please ignore this email.

Regards,
Classroom Team
==========================================================

==========================================================
Email sent at: 2025-05-16 15:59:52
To: <EMAIL>
Subject: Classroom - Account Recovery
Headers: From: <EMAIL>

----------------------------------------------------------
Hello,

You have requested to recover your account at Classroom.

Your username is: maxceljane.narisma

To reset your password, use the following verification code:
265187

This code will expire in 1 hour.

If you did not request this, please ignore this email.

Regards,
Classroom Team
==========================================================

==========================================================
Email sent at: 2025-05-16 16:01:57
To: <EMAIL>
Subject: Classroom - Account Recovery
Headers: From: <EMAIL>

----------------------------------------------------------
Hello,

You have requested to recover your account at Classroom.

Your username is: maxceljane.narisma

To reset your password, use the following verification code:
484980

This code will expire in 1 hour.

If you did not request this, please ignore this email.

Regards,
Classroom Team
==========================================================

==========================================================
Email sent at: 2025-05-16 16:16:28
To: <EMAIL>
Subject: Classroom - Account Recovery
Headers: From: <EMAIL>

----------------------------------------------------------
Hello,

You have requested to recover your account at Classroom.

Your username is: maxceljane.narisma

To reset your password, use the following verification code:
387367

This code will expire in 1 hour.

If you did not request this, please ignore this email.

Regards,
Classroom Team
==========================================================

==========================================================
Email sent at: 2025-05-16 16:19:10
To: <EMAIL>
Subject: Classroom - Account Recovery
Headers: From: <EMAIL>

----------------------------------------------------------
Hello,

You have requested to recover your account at Classroom.

Your username is: maxceljane.narisma

To reset your password, use the following verification code:
624889

This code will expire in 1 hour.

If you did not request this, please ignore this email.

Regards,
Classroom Team
==========================================================

==========================================================
Email sent at: 2025-05-16 16:28:40
To: <EMAIL>
Subject: Classroom - Account Recovery
Headers: 
  From: Classroom <<EMAIL>>
  X-Mailer: PHP/8.0.30
  MIME-Version: 1.0
  Content-Type: text/plain; charset=UTF-8
----------------------------------------------------------
Hello,

You have requested to recover your account at Classroom.

Your username is: maxceljane.narisma

To reset your password, use the following verification code:
608254

This code will expire in 1 hour.

If you did not request this, please ignore this email.

Regards,
Classroom Team
==========================================================

==========================================================
Email sent at: 2025-05-16 16:30:55
To: <EMAIL>
Subject: Classroom - Account Recovery
Headers: 
  From: Classroom <<EMAIL>>
  X-Mailer: PHP/8.0.30
  MIME-Version: 1.0
  Content-Type: text/plain; charset=UTF-8
----------------------------------------------------------
Hello,

You have requested to recover your account at Classroom.

Your username is: maxceljane.narisma

To reset your password, use the following verification code:
003310

This code will expire in 1 hour.

If you did not request this, please ignore this email.

Regards,
Classroom Team
==========================================================

==========================================================
Email sent at: 2025-05-16 16:35:03
To: <EMAIL>
Subject: Classroom - Account Recovery
Headers: 
  From: Classroom <<EMAIL>>
  X-Mailer: PHP/8.0.30
  MIME-Version: 1.0
  Content-Type: text/plain; charset=UTF-8
----------------------------------------------------------
Hello,

You have requested to recover your account at Classroom.

Your username is: maxceljane.narisma

To reset your password, use the following verification code:
139036

This code will expire in 1 hour.

If you did not request this, please ignore this email.

Regards,
Classroom Team
==========================================================

2025-05-27 10:26:48 | TO: <EMAIL> | SUBJECT: Classroom - Account Recovery | STATUS: ERROR: Email sending failed: SMTP Error: Could not authenticate.
2025-05-27 10:26:51 | TO: <EMAIL> | SUBJECT: Classroom - Account Recovery | STATUS: ERROR: Email sending failed: SMTP Error: Could not authenticate.
2025-05-27 10:26:54 | TO: <EMAIL> | SUBJECT: Classroom - Account Recovery | STATUS: ERROR: Email sending failed: SMTP Error: Could not authenticate.
2025-05-27 10:27:00 | TO: <EMAIL> | SUBJECT: Classroom - Account Recovery | STATUS: ERROR: Email sending failed: SMTP Error: Could not authenticate.
2025-05-27 10:27:04 | TO: <EMAIL> | SUBJECT: Classroom - Account Recovery | STATUS: ERROR: Email sending failed: SMTP Error: Could not authenticate.
2025-05-27 10:32:03 | TO: <EMAIL> | SUBJECT: Classroom - Account Recovery | STATUS: ERROR: Email sending failed: SMTP Error: Could not authenticate.
2025-05-27 10:32:20 | TO: <EMAIL> | SUBJECT: Classroom - Account Recovery | STATUS: ERROR: Email sending failed: SMTP Error: Could not authenticate.
2025-05-27 10:32:39 | TO: <EMAIL> | SUBJECT: Classroom - Account Recovery | STATUS: ERROR: Email sending failed: SMTP Error: Could not authenticate.
2025-05-27 10:35:08 | TO: <EMAIL> | SUBJECT: Classroom - Account Recovery | STATUS: ERROR: Email sending failed: SMTP Error: Could not authenticate.
2025-05-27 10:36:34 | TO: <EMAIL> | SUBJECT: Classroom - Account Recovery | STATUS: ERROR: Email sending failed: SMTP Error: Could not authenticate.
