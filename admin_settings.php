<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/settings_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if the user is an admin
if (!isAdmin()) {
    $_SESSION['error'] = "Only administrators can access system settings.";
    header("location: index.php");
    exit;
}

// Initialize variables
$success = $error = "";

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Update max file upload size
    if (isset($_POST['max_file_upload_size'])) {
        $maxSize = intval($_POST['max_file_upload_size']);
        if ($maxSize <= 0) {
            $error = "Maximum file upload size must be greater than 0.";
        } else {
            $result = updateSetting('max_file_upload_size', $maxSize);
            if ($result === true) {
                $success = "Settings updated successfully.";
            } else {
                $error = $result;
            }
        }
    }

    // Update allowed file types
    if (isset($_POST['allowed_file_types']) && is_array($_POST['allowed_file_types'])) {
        $allowedTypes = implode(',', $_POST['allowed_file_types']);
        $result = updateSetting('allowed_file_types', $allowedTypes);
        if ($result === true) {
            $success = "Settings updated successfully.";
        } else {
            $error = $result;
        }
    }

    // Update site name
    if (isset($_POST['site_name'])) {
        $siteName = trim($_POST['site_name']);
        if (!empty($siteName)) {
            $result = updateSetting('site_name', $siteName);
            if ($result === true) {
                $success = "Settings updated successfully.";
            } else {
                $error = $result;
            }
        }
    }

    // Update site description
    if (isset($_POST['site_description'])) {
        $siteDescription = trim($_POST['site_description']);
        $result = updateSetting('site_description', $siteDescription);
        if ($result === true) {
            $success = "Settings updated successfully.";
        } else {
            $error = $result;
        }
    }

    // Update admin email
    if (isset($_POST['admin_email'])) {
        $adminEmail = trim($_POST['admin_email']);
        if (!empty($adminEmail) && filter_var($adminEmail, FILTER_VALIDATE_EMAIL)) {
            $result = updateSetting('admin_email', $adminEmail);
            if ($result === true) {
                $success = "Settings updated successfully.";
            } else {
                $error = $result;
            }
        } else if (!empty($adminEmail)) {
            $error = "Please enter a valid email address.";
        }
    }

    // Update maintenance mode
    $maintenanceMode = isset($_POST['maintenance_mode']) ? 1 : 0;
    $result = updateSetting('maintenance_mode', $maintenanceMode);
    if ($result === true) {
        $success = "Settings updated successfully.";
    } else {
        $error = $result;
    }
}

// Get current settings
$maxFileUploadSize = getSetting('max_file_upload_size', 5);
$allowedFileTypes = getAllowedFileTypes();
$siteName = getSetting('site_name', 'E-Learning System');
$siteDescription = getSetting('site_description', 'A comprehensive e-learning platform');
$adminEmail = getSetting('admin_email', '<EMAIL>');
$maintenanceMode = getSetting('maintenance_mode', 0);

// Set page title
$page_title = "System Settings";

// Include header
require_once 'includes/header.php';
?>

<!-- Page header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>System Settings</h1>
</div>

<?php if (!empty($success)): ?>
<div class="alert alert-success">
    <?php echo $success; ?>
</div>
<?php endif; ?>

<?php if (!empty($error)): ?>
<div class="alert alert-danger">
    <?php echo $error; ?>
</div>
<?php endif; ?>

<!-- Settings form -->
<div class="card">
    <div class="card-body">
        <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
            <h4 class="mb-3">File Upload Settings</h4>
            <div class="form-group">
                <label for="max_file_upload_size">Maximum File Upload Size (MB)</label>
                <input type="number" name="max_file_upload_size" id="max_file_upload_size" class="form-control" value="<?php echo $maxFileUploadSize; ?>" min="1" max="100">
                <small class="form-text text-muted">Maximum file size in megabytes (MB) that users can upload.</small>
            </div>

            <div class="form-group">
                <label>Allowed File Types</label>
                <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" id="type_image" name="allowed_file_types[]" value="image/jpeg,image/png,image/gif" <?php echo (strpos(implode(',', $allowedFileTypes), 'image/jpeg') !== false) ? 'checked' : ''; ?>>
                    <label class="custom-control-label" for="type_image">Images (JPEG, PNG, GIF)</label>
                </div>
                <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" id="type_pdf" name="allowed_file_types[]" value="application/pdf" <?php echo (strpos(implode(',', $allowedFileTypes), 'application/pdf') !== false) ? 'checked' : ''; ?>>
                    <label class="custom-control-label" for="type_pdf">PDF Documents</label>
                </div>
                <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" id="type_office" name="allowed_file_types[]" value="application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation" <?php echo (strpos(implode(',', $allowedFileTypes), 'application/msword') !== false) ? 'checked' : ''; ?>>
                    <label class="custom-control-label" for="type_office">Office Documents (Word, Excel, PowerPoint)</label>
                </div>
                <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" id="type_text" name="allowed_file_types[]" value="text/plain" <?php echo (strpos(implode(',', $allowedFileTypes), 'text/plain') !== false) ? 'checked' : ''; ?>>
                    <label class="custom-control-label" for="type_text">Text Files</label>
                </div>
                <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" id="type_archive" name="allowed_file_types[]" value="application/zip,application/x-rar-compressed" <?php echo (strpos(implode(',', $allowedFileTypes), 'application/zip') !== false) ? 'checked' : ''; ?>>
                    <label class="custom-control-label" for="type_archive">Archives (ZIP, RAR)</label>
                </div>
            </div>

            <h4 class="mb-3 mt-4">General Settings</h4>
            <div class="form-group">
                <label for="site_name">Site Name</label>
                <input type="text" name="site_name" id="site_name" class="form-control" value="<?php echo htmlspecialchars($siteName); ?>">
            </div>

            <div class="form-group">
                <label for="site_description">Site Description</label>
                <textarea name="site_description" id="site_description" class="form-control" rows="3"><?php echo htmlspecialchars($siteDescription); ?></textarea>
            </div>

            <div class="form-group">
                <label for="admin_email">Admin Email</label>
                <input type="email" name="admin_email" id="admin_email" class="form-control" value="<?php echo htmlspecialchars($adminEmail); ?>">
            </div>

            <div class="form-group">
                <div class="custom-control custom-switch">
                    <input type="checkbox" class="custom-control-input" id="maintenance_mode" name="maintenance_mode" <?php echo ($maintenanceMode == 1) ? 'checked' : ''; ?>>
                    <label class="custom-control-label" for="maintenance_mode">Maintenance Mode</label>
                </div>
                <small class="form-text text-muted">When enabled, only administrators can access the site.</small>
            </div>

            <h4 class="mb-3 mt-4">Email System</h4>
            <div class="row">
                <div class="col-md-6">
                    <div class="card border-info">
                        <div class="card-body text-center">
                            <i class="fas fa-envelope-open-text fa-3x text-info mb-3"></i>
                            <h6>Email Status</h6>
                            <p class="text-muted">View email configuration and activity</p>
                            <a href="email_status.php" class="btn btn-info">
                                <i class="fas fa-chart-line"></i> View Status
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-primary">
                        <div class="card-body text-center">
                            <i class="fas fa-cog fa-3x text-primary mb-3"></i>
                            <h6>Email Settings</h6>
                            <p class="text-muted">Configure SMTP and email options</p>
                            <a href="email_settings.php" class="btn btn-primary">
                                <i class="fas fa-cog"></i> Configure
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group mb-0 mt-4">
                <button type="submit" class="btn btn-primary">Save Settings</button>
                <a href="index.php" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
