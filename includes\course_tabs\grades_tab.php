<?php
// Grades tab content - only visible to teachers/instructors
// Shows student grades for all assignments
require_once 'includes/submission_functions.php';
?>
<div id="grades-tab" class="course-tab-content">
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Student Grades</h5>
            <div>
                <button class="btn btn-sm btn-outline-primary mr-2" id="exportGrades">
                    <i class="fas fa-file-export"></i> Export
                </button>
                <div class="dropdown d-inline-block">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="gradesViewDropdown" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="fas fa-eye"></i> View
                    </button>
                    <div class="dropdown-menu dropdown-menu-right" aria-labelledby="gradesViewDropdown">
                        <a class="dropdown-item active" href="#" data-view="all">All Assignments</a>
                        <a class="dropdown-item" href="#" data-view="missing">Missing Work</a>
                        <a class="dropdown-item" href="#" data-view="graded">Graded Work</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover grades-table mb-0">
                    <thead>
                        <tr>
                            <th style="min-width: 200px;">Student Name</th>
                            <?php
                            // Get all assignments for this course
                            $allAssignments = getCourseAssignments($courseId);
                            if (!is_string($allAssignments) && count($allAssignments) > 0):
                                foreach ($allAssignments as $assignment):
                            ?>
                                <th class="text-center" style="min-width: 120px;" title="<?php echo htmlspecialchars($assignment['title']); ?>">
                                    <?php echo htmlspecialchars(substr($assignment['title'], 0, 15) . (strlen($assignment['title']) > 15 ? '...' : '')); ?>
                                    <small class="d-block text-muted"><?php echo $assignment['points']; ?> pts</small>
                                </th>
                            <?php
                                endforeach;
                            endif;
                            ?>
                            <th class="text-center" style="min-width: 100px;">Overall</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        // Get all enrolled students
                        if (count($students) > 0):
                            foreach ($students as $student):
                                // Get student's submissions for this course
                                $submissions = getStudentSubmissions($courseId, $student['user_id']);
                                if (is_string($submissions)) {
                                    $submissions = [];
                                }

                                // Calculate overall grade
                                $totalPoints = 0;
                                $earnedPoints = 0;
                                $gradedAssignments = 0;
                        ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="user-avatar sm mr-2">
                                            <?php echo strtoupper(substr($student['first_name'], 0, 1)); ?>
                                        </div>
                                        <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?>
                                    </div>
                                </td>
                                <?php
                                if (!is_string($allAssignments) && count($allAssignments) > 0):
                                    foreach ($allAssignments as $assignment):
                                        // Find submission for this assignment
                                        $submission = null;
                                        foreach ($submissions as $sub) {
                                            if ($sub['assignment_id'] == $assignment['assignment_id']) {
                                                $submission = $sub;
                                                break;
                                            }
                                        }

                                        // Update total points
                                        $totalPoints += $assignment['points'];

                                        // If graded, update earned points
                                        if ($submission && $submission['is_graded']) {
                                            // For auto-graded activities, the score field contains the actual points earned
                                            $pointsEarned = $submission['score'];
                                            $pointsEarned = round($pointsEarned);
                                            $earnedPoints += $pointsEarned;
                                            $gradedAssignments++;
                                        }
                                ?>
                                    <td class="text-center">
                                        <?php if ($submission): ?>
                                            <?php if ($submission['is_graded']): ?>
                                                <a href="submission_view.php?id=<?php echo $submission['submission_id']; ?>" class="grade-cell">
                                                    <?php
                                                    // Calculate actual points earned based on percentage grade
                                                    $earnedPoints = ($submission['score'] / 100) * $assignment['points'];
                                                    $earnedPoints = round($earnedPoints);

                                                    $percentage = ($earnedPoints / $assignment['points']) * 100;
                                                    $gradeClass = 'text-danger';
                                                    if ($percentage >= 90) {
                                                        $gradeClass = 'text-success';
                                                    } elseif ($percentage >= 80) {
                                                        $gradeClass = 'text-primary';
                                                    } elseif ($percentage >= 70) {
                                                        $gradeClass = 'text-info';
                                                    } elseif ($percentage >= 60) {
                                                        $gradeClass = 'text-warning';
                                                    }
                                                    ?>
                                                    <span class="<?php echo $gradeClass; ?>"><?php echo $earnedPoints; ?>/<?php echo $assignment['points']; ?></span>
                                                </a>
                                            <?php else: ?>
                                                <a href="submission_grade.php?id=<?php echo $submission['submission_id']; ?>" class="grade-cell">
                                                    <span class="badge badge-secondary">Submitted</span>
                                                </a>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <?php if (strtotime($assignment['due_date']) < time()): ?>
                                                <span class="badge badge-danger">Missing</span>
                                            <?php else: ?>
                                                <span class="badge badge-light">Not submitted</span>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </td>
                                <?php
                                    endforeach;
                                endif;

                                // Calculate overall percentage
                                $overallPercentage = $gradedAssignments > 0 ? ($earnedPoints / $totalPoints) * 100 : 0;
                                $overallGradeClass = 'text-danger';
                                if ($overallPercentage >= 90) {
                                    $overallGradeClass = 'text-success';
                                } elseif ($overallPercentage >= 80) {
                                    $overallGradeClass = 'text-primary';
                                } elseif ($overallPercentage >= 70) {
                                    $overallGradeClass = 'text-info';
                                } elseif ($overallPercentage >= 60) {
                                    $overallGradeClass = 'text-warning';
                                }
                                ?>
                                <td class="text-center">
                                    <span class="<?php echo $overallGradeClass; ?> font-weight-bold">
                                        <?php echo number_format($overallPercentage, 1); ?>%
                                    </span>
                                </td>
                            </tr>
                        <?php
                            endforeach;
                        else:
                        ?>
                            <tr>
                                <td colspan="<?php echo count($allAssignments) + 2; ?>" class="text-center py-4">
                                    <p class="text-muted mb-0">No students enrolled yet</p>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Export grades functionality
    document.getElementById('exportGrades').addEventListener('click', function() {
        // Simple CSV export
        let csv = [];
        let rows = document.querySelectorAll('.grades-table tr');

        for (let i = 0; i < rows.length; i++) {
            let row = [], cols = rows[i].querySelectorAll('td, th');

            for (let j = 0; j < cols.length; j++) {
                // Get the text content, removing any extra whitespace
                let text = cols[j].textContent.replace(/\s+/g, ' ').trim();

                // Escape double quotes and wrap in quotes
                text = '"' + text.replace(/"/g, '""') + '"';

                row.push(text);
            }

            csv.push(row.join(','));
        }

        // Download the CSV file
        let csvContent = csv.join('\n');
        let blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        let url = URL.createObjectURL(blob);

        let link = document.createElement('a');
        link.setAttribute('href', url);
        link.setAttribute('download', 'course_grades.csv');
        link.style.visibility = 'hidden';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    });

    // View filter functionality
    document.querySelectorAll('[data-view]').forEach(function(element) {
        element.addEventListener('click', function(e) {
            e.preventDefault();

            // Update active class
            document.querySelectorAll('[data-view]').forEach(function(el) {
                el.classList.remove('active');
            });
            this.classList.add('active');

            let view = this.getAttribute('data-view');
            let rows = document.querySelectorAll('.grades-table tbody tr');

            if (view === 'all') {
                // Show all rows
                rows.forEach(function(row) {
                    row.style.display = '';
                });
            } else if (view === 'missing') {
                // Show only rows with missing assignments
                rows.forEach(function(row) {
                    if (row.querySelector('.badge-danger')) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            } else if (view === 'graded') {
                // Show only rows with graded assignments
                rows.forEach(function(row) {
                    if (row.querySelector('.text-success, .text-primary, .text-info, .text-warning, .text-danger')) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            }
        });
    });
});
</script>
