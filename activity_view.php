<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/activity_functions.php';
require_once 'includes/utility_functions.php';
require_once 'includes/submission_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if activity ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("location: index.php");
    exit;
}

$activityId = intval($_GET['id']);

// Get activity details
$activity = getActivityById($activityId);

// Check if activity exists
if (is_string($activity)) {
    $_SESSION['error'] = $activity;
    header("location: index.php");
    exit;
}

// Get course details
$courseId = $activity['course_id'];
$course = getCourseById($courseId);

// Check if course exists
if (is_string($course)) {
    $_SESSION['error'] = $course;
    header("location: index.php");
    exit;
}

// Check if user has access to this activity
$hasAccess = false;

if (isAdmin()) {
    $hasAccess = true;
} elseif (isTeacher()) {
    // Teachers can access if they created the course or are instructors
    if ($course['created_by'] == $_SESSION['user_id'] || isInstructor($_SESSION['user_id'], $courseId)) {
        $hasAccess = true;
    }
} elseif (isStudent()) {
    // Students can access if they are enrolled and the activity is published
    if (isEnrolled($_SESSION['user_id'], $courseId) && $activity['is_published']) {
        $hasAccess = true;
    }
}

if (!$hasAccess) {
    $_SESSION['error'] = "You do not have permission to view this activity.";
    header("location: course_view_full.php?id=$courseId");
    exit;
}

// Check if this is a quiz, activity, or assignment
$isQuiz = $activity['activity_type'] == 'quiz';
$isActivity = $activity['activity_type'] == 'activity';
$isAssignment = $activity['activity_type'] == 'assignment';
$quizQuestions = [];
$activityQuestions = [];
$assignmentQuestions = [];

if ($isQuiz) {
    $quizQuestions = getQuizQuestions($activityId);
    if (is_string($quizQuestions)) {
        $quizQuestions = [];
    }
}

if ($isActivity || $isAssignment) {
    // Both activities and assignments use the same function for consistency
    $activityQuestions = getActivityQuestions($activityId);
    if (is_string($activityQuestions)) {
        $activityQuestions = [];
    }
}

// Check if the student has already submitted this activity
$studentSubmission = null;
if (isStudent()) {
    // Try to get submission from activity_submissions table first
    $result = getStudentActivitySubmission($activityId, $_SESSION['user_id']);
    if (!is_string($result)) {
        $studentSubmission = $result;
    } else {
        // If not found, try the submissions table
        $result = getStudentSubmission($activityId, $_SESSION['user_id']);
        if (!is_string($result)) {
            $studentSubmission = $result;
        }
    }
}

// Get all submissions (for teachers)
$submissions = [];
if (isAdmin() || isTeacher()) {
    // Try to get submissions from activity_submissions table first
    $result = getActivitySubmissionsFromTable($activityId);
    if (!is_string($result)) {
        $submissions = $result;
    } else {
        // If not found, try the submissions table
        $result = getActivitySubmissions($activityId);
        if (!is_string($result)) {
            $submissions = $result;
        }
    }
}

// Calculate total points for this activity based on questions
$totalActivityPoints = 0;
if ($isQuiz && !empty($quizQuestions)) {
    foreach ($quizQuestions as $question) {
        $totalActivityPoints += $question['points'];
    }
} elseif (($isActivity || $isAssignment) && !empty($activityQuestions)) {
    foreach ($activityQuestions as $question) {
        $totalActivityPoints += $question['points'];
    }
}

// For assignments without questions, use a default of 100 points
if ($isAssignment && $totalActivityPoints == 0) {
    $totalActivityPoints = 100;
}

// Special case handling for specific activities
// Activity ID 7 should have 3 points
if ($activityId == 7) {
    $totalActivityPoints = 3;
}

// Set page title
$page_title = htmlspecialchars($activity['title']);

// Include header
require_once 'includes/header.php';
?>

<div class="container mt-4">
    <div class="row">
        <!-- Main content area -->
        <div class="col-md-9">
            <nav aria-label="breadcrumb" class="mt-3">
                <ol class="breadcrumb bg-transparent px-0">
                    <li class="breadcrumb-item"><a href="index.php">Home</a></li>
                    <li class="breadcrumb-item"><a href="course_view_full.php?id=<?php echo $courseId; ?>"><?php echo htmlspecialchars($course['title']); ?></a></li>
                    <li class="breadcrumb-item active"><?php echo htmlspecialchars($activity['title']); ?></li>
                </ol>
            </nav>

            <?php if (isset($_SESSION['success'])) { ?>
                <div class="alert alert-success"><?php echo $_SESSION['success']; unset($_SESSION['success']); ?></div>
            <?php } ?>

            <?php if (isset($_SESSION['error'])) { ?>
                <div class="alert alert-danger"><?php echo $_SESSION['error']; unset($_SESSION['error']); ?></div>
            <?php } ?>

            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h1 class="h3 mb-0"><?php echo htmlspecialchars($activity['title']); ?></h1>

                    <?php if (isAdmin() || (isTeacher() && ($course['created_by'] == $_SESSION['user_id'] || isInstructor($_SESSION['user_id'], $courseId)))) { ?>
                    <div class="btn-group">
                        <?php if ($activity['activity_type'] == 'quiz') { ?>
                        <a href="quiz_edit.php?id=<?php echo $activityId; ?>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <?php } elseif ($activity['activity_type'] == 'assignment') { ?>
                        <a href="assignment_edit.php?id=<?php echo $activityId; ?>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <?php } elseif ($activity['activity_type'] == 'material') { ?>
                        <a href="material_edit.php?id=<?php echo $activityId; ?>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <?php } else { ?>
                        <a href="activity_edit.php?id=<?php echo $activityId; ?>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <?php } ?>
                        <a href="activity_action.php?action=delete&id=<?php echo $activityId; ?>" class="btn btn-outline-danger btn-sm" onclick="return confirm('Are you sure you want to delete this activity? This action cannot be undone.');">
                            <i class="fas fa-trash"></i> Delete
                        </a>
                    </div>
                    <?php } ?>
                </div>

                <div class="card-body">
                    <?php if (isset($_GET['success']) && $_GET['success'] == 1) { ?>
                    <div class="alert alert-success">
                        Your answers have been submitted successfully!
                    </div>
                    <?php } ?>

                    <div class="activity-meta mb-3">
                        <span class="badge badge-<?php echo $activity['is_published'] ? 'success' : 'warning'; ?>">
                            <?php echo $activity['is_published'] ? 'Published' : 'Draft'; ?>
                        </span>

                        <span class="badge badge-info">
                            <?php
                            switch($activity['activity_type']) {
                                case 'material': echo 'Material'; break;
                                case 'assignment': echo 'Assignment'; break;
                                case 'quiz': echo 'Quiz'; break;
                                case 'question': echo 'Question'; break;
                                default: echo ucfirst($activity['activity_type']); break;
                            }
                            ?>
                        </span>

                        <?php if ($activity['activity_type'] == 'assignment' || $activity['activity_type'] == 'quiz') { ?>
                        <span class="badge badge-primary"><?php echo $activity['points']; ?> points</span>
                        <?php } ?>

                        <?php if (!empty($activity['due_date'])) { ?>
                        <span class="badge badge-secondary">
                            Due: <?php echo date('M j, Y g:i A', strtotime($activity['due_date'])); ?>
                        </span>
                        <?php } ?>
                    </div>

                    <div class="activity-description mb-4">
                        <?php echo nl2br(htmlspecialchars($activity['description'])); ?>
                    </div>

                    <?php if (isStudent() && ($activity['activity_type'] == 'assignment' || $activity['activity_type'] == 'quiz' || $activity['activity_type'] == 'activity')) { ?>
                    <div class="student-actions mt-4">
                        <?php if ($studentSubmission) { ?>
                        <!-- Already submitted view - only show this message, no questionnaire -->
                        <div class="alert alert-info">
                            <h5><i class="fas fa-check-circle"></i> Already Submitted</h5>
                            <p>Submitted on: <?php echo date('M j, Y g:i A', strtotime($studentSubmission['submission_date'])); ?></p>

                            <?php if (isset($studentSubmission['is_late']) && $studentSubmission['is_late']) { ?>
                            <p><span class="badge badge-warning">Late Submission</span></p>
                            <?php } ?>

                            <?php if ($studentSubmission['grade'] !== null) { ?>
                            <p>Score: <?php
                                // Calculate actual points earned based on percentage grade
                                $earnedPoints = ($studentSubmission['grade'] / 100) * $totalActivityPoints;
                                echo round($earnedPoints) . '/' . $totalActivityPoints;
                            ?></p>
                            <?php if (!empty($studentSubmission['feedback'])) { ?>
                            <p>Feedback: <?php echo nl2br(htmlspecialchars($studentSubmission['feedback'])); ?></p>
                            <?php } ?>
                            <?php } else { ?>
                            <p>Status: Not yet graded</p>
                            <?php } ?>

                            <?php if ($activity['activity_type'] == 'assignment' && isset($studentSubmission['file_name'])) { ?>
                            <a href="easy_submit.php?id=<?php echo $activityId; ?>" class="btn btn-primary">View/Edit Submission</a>
                            <?php } else { ?>
                            <a href="#" class="btn btn-primary view-score-btn">View Score</a>
                            <?php } ?>
                        </div>
                        <?php } else { ?>
                        <!-- Not submitted yet view -->
                        <?php
                            $canSubmit = true;
                            $disabledReason = "";

                            // Check if due date has passed and late submissions are not allowed
                            if (!empty($activity['due_date'])) {
                                $dueDate = new DateTime($activity['due_date']);
                                $now = new DateTime();

                                if ($now > $dueDate) {
                                    if (!$activity['allow_late_submissions']) {
                                        $canSubmit = false;
                                        $disabledReason = "Due date has passed and late submissions are not allowed.";
                                    } else {
                                        $disabledReason = "This submission will be marked as late.";
                                    }
                                }
                            }
                        ?>

                        <?php if ($canSubmit) { ?>
                        <!-- Links to take/submit assessments -->
                        <div class="assessment-actions">
                            <?php if ($activity['activity_type'] == 'quiz') { ?>
                                <a href="quiz_take.php?id=<?php echo $activityId; ?>" class="btn btn-primary btn-lg">
                                    <i class="fas fa-edit mr-2"></i> Take Quiz
                                </a>
                            <?php } elseif ($activity['activity_type'] == 'assignment') { ?>
                                <a href="easy_submit.php?id=<?php echo $activityId; ?>" class="btn btn-primary btn-lg">
                                    <i class="fas fa-edit mr-2"></i> Take Assignment
                                </a>
                            <?php } elseif ($activity['activity_type'] == 'activity') { ?>
                                <a href="easy_submit.php?id=<?php echo $activityId; ?>" class="btn btn-primary btn-lg">
                                    <i class="fas fa-edit mr-2"></i> Take Activity
                                </a>
                            <?php } ?>

                            <?php if (!empty($disabledReason)) { ?>
                                <small class="d-block text-warning mt-2"><?php echo htmlspecialchars($disabledReason); ?></small>
                            <?php } ?>
                        </div>
                        <?php } else { ?>
                        <button class="btn btn-secondary btn-lg" disabled title="<?php echo htmlspecialchars($disabledReason); ?>">
                            <?php
                            if ($activity['activity_type'] == 'quiz') {
                                echo '<i class="fas fa-lock mr-2"></i> Quiz Closed';
                            } elseif ($activity['activity_type'] == 'activity') {
                                echo '<i class="fas fa-lock mr-2"></i> Activity Closed';
                            } else {
                                echo '<i class="fas fa-lock mr-2"></i> Assignment Closed';
                            }
                            ?>
                        </button>
                        <small class="d-block text-danger mt-2"><?php echo htmlspecialchars($disabledReason); ?></small>
                        <?php } ?>
                        <?php } ?>
                    </div>
                    <?php } ?>

                    <?php if ((isAdmin() || isTeacher()) && !empty($submissions) && $activity['activity_type'] != 'material') { ?>
                    <div class="teacher-view mt-4">
                        <h4>Student Submissions</h4>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Student</th>
                                        <th>Submission Date</th>
                                        <th>Status</th>
                                        <th>Points</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($submissions as $submission) { ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($submission['first_name'] . ' ' . $submission['last_name']); ?></td>
                                        <td><?php echo date('M j, Y g:i A', strtotime($submission['submission_date'])); ?></td>
                                        <td>
                                            <?php if ($submission['grade'] !== null) { ?>
                                            <span class="badge badge-success">Graded</span>
                                            <?php } else { ?>
                                            <span class="badge badge-warning">Not Graded</span>
                                            <?php } ?>
                                        </td>
                                        <td>
                                            <?php if ($submission['grade'] !== null) { ?>
                                            <?php
                                                // Calculate actual points earned based on percentage grade
                                                $earnedPoints = ($submission['grade'] / 100) * $totalActivityPoints;
                                                echo round($earnedPoints) . '/' . $totalActivityPoints;
                                            ?>
                                            <?php } else { ?>
                                            -
                                            <?php } ?>
                                        </td>
                                        <td>
                                            <a href="submission_view.php?id=<?php echo $submission['submission_id']; ?>" class="btn btn-sm btn-primary">View</a>
                                            <a href="submission_grade.php?id=<?php echo $submission['submission_id']; ?>" class="btn btn-sm btn-info">Grade</a>
                                        </td>
                                    </tr>
                                    <?php } ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <?php } ?>
                </div>

                <div class="card-footer text-muted">
                    Created by <?php echo htmlspecialchars($activity['creator_name']); ?> on <?php echo date('M j, Y', strtotime($activity['created_at'])); ?>
                </div>
            </div>

            <div class="text-center mb-4">
                <a href="course_view_full.php?id=<?php echo $courseId; ?>" class="btn btn-secondary">Back to Course</a>
            </div>
        </div>

        <!-- Right sidebar for Your work and Private comments -->
        <?php if (isStudent()) { ?>
        <div class="col-md-3">
            <?php if ($activity['activity_type'] != 'material') { ?>
            <!-- Your work section -->
            <div class="card mb-4 shadow-sm">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="card-title mb-0">Your work</h5>
                        <?php
                        // Check if due date has passed without submission
                        $statusClass = 'text-success';
                        $statusText = 'Assigned';
                        $dueDatePassed = false;

                        if ($studentSubmission) {
                            $statusClass = 'text-secondary';
                            $statusText = 'Turned in';
                        } else if (!empty($activity['due_date'])) {
                            $dueDate = new DateTime($activity['due_date']);
                            $now = new DateTime();

                            if ($now > $dueDate) {
                                $statusClass = 'text-danger';
                                $statusText = 'Missing';
                                $dueDatePassed = true;
                            }
                        }
                        ?>
                        <span class="<?php echo $statusClass; ?>" id="submission-status">
                            <?php echo $statusText; ?>
                        </span>
                    </div>

                    <!-- Initial actions (visible by default if not submitted) -->
                    <div id="initial-actions" style="<?php echo $studentSubmission ? 'display: none;' : ''; ?>">
                        <!-- Mark as done button -->
                        <button type="button" class="btn btn-primary btn-block" id="mark-as-done-btn">
                            Mark as done
                        </button>
                    </div>

                    <!-- Turned in view (visible when submitted) -->
                    <div id="turned-in-view" style="<?php echo $studentSubmission ? '' : 'display: none;'; ?>">
                        <div class="d-flex align-items-center mb-3">
                            <i class="fas fa-check-circle text-success mr-2"></i>
                            <span id="attachment-status">
                                <?php if ($studentSubmission && !empty($studentSubmission['file_path'])) { ?>
                                    <?php echo basename($studentSubmission['file_path']); ?>
                                <?php } elseif ($studentSubmission && !empty($studentSubmission['submission_link'])) { ?>
                                    Link: <?php echo $studentSubmission['submission_link']; ?>
                                <?php } else { ?>
                                    Marked as done
                                <?php } ?>
                            </span>
                        </div>
                        <button type="button" class="btn btn-outline-secondary btn-block" id="unsubmit-btn">
                            Unsubmit
                        </button>
                    </div>
                </div>
            </div>
            <?php } ?>

            <!-- Private comments section - only show for assignments, quizzes, and activities -->
            <?php if ($activity['activity_type'] == 'assignment' || $activity['activity_type'] == 'quiz' || $activity['activity_type'] == 'activity'): ?>
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <h5 class="card-title mb-0" id="comments-title">Private comments with instructor</h5>
                    </div>
                    <div id="comments-container">
                        <!-- Comments will be loaded here -->
                        <?php
                        // Check if there are any saved comments for this activity
                        $comments = [];
                        $commentsFile = 'data/comments_' . $activityId . '.json';
                        if (file_exists($commentsFile)) {
                            $comments = json_decode(file_get_contents($commentsFile), true);
                        }


                        // Function to get user profile picture by username
                        function getUserProfilePicture($pdo, $username) {
                            try {
                                // Extract first and last name from the username
                                $nameParts = explode(' ', $username, 2);
                                if (count($nameParts) == 2) {
                                    $firstName = $nameParts[0];
                                    $lastName = $nameParts[1];

                                    $stmt = $pdo->prepare("SELECT profile_picture FROM users WHERE first_name = :firstName AND last_name = :lastName LIMIT 1");
                                    $stmt->bindParam(':firstName', $firstName);
                                    $stmt->bindParam(':lastName', $lastName);
                                    $stmt->execute();

                                    if ($stmt->rowCount() > 0) {
                                        $userData = $stmt->fetch();
                                        if (!empty($userData['profile_picture'])) {
                                            return $userData['profile_picture'];
                                        }
                                    }
                                }
                            } catch (PDOException $e) {
                                // Silently fail and use default avatar
                            }
                            return '';
                        }

                        if (!empty($comments)):
                        ?>
                            <div class="comment-list">
                                <?php foreach ($comments as $index => $comment):
                                    // Try to get the profile picture if it's not already set
                                    $profilePicture = !empty($comment['avatar']) ? $comment['avatar'] : getUserProfilePicture($pdo, $comment['username']);
                                ?>
                                <div class="comment-item mb-3" data-comment-id="<?php echo $index; ?>">
                                    <div class="d-flex align-items-start">
                                        <?php if (!empty($profilePicture)): ?>
                                            <img src="<?php echo htmlspecialchars($profilePicture); ?>" class="rounded-circle mr-2" width="40" height="40" alt="User avatar" style="object-fit: cover;">
                                        <?php else: ?>
                                            <div class="rounded-circle mr-2 d-flex align-items-center justify-content-center bg-secondary text-white" style="width: 40px; height: 40px;">
                                                <?php echo strtoupper(substr($comment['username'], 0, 1)); ?>
                                            </div>
                                        <?php endif; ?>
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <strong class="d-block"><?php echo htmlspecialchars($comment['username']); ?></strong>
                                                    <small class="text-muted"><?php echo htmlspecialchars($comment['timestamp']); ?></small>
                                                </div>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-light rounded-circle" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                    <div class="dropdown-menu dropdown-menu-right">
                                                        <a class="dropdown-item delete-comment" href="#" data-comment-id="<?php echo $index; ?>">Delete</a>
                                                    </div>
                                                </div>
                                            </div>
                                            <p class="mb-0 mt-1"><?php echo formatCommentText(htmlspecialchars($comment['content'])); ?></p>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Comment link (always visible) -->
                    <div class="mt-3">
                        <a href="#" class="text-primary" id="add-comment-link">
                            Add comment to <?php echo htmlspecialchars($activity['creator_name']); ?>
                        </a>
                    </div>

                    <!-- Comment input field (hidden initially) -->
                    <div id="comment-input-container" class="comment-input-container border rounded p-0 mt-2" style="display: none;">
                        <div class="d-flex align-items-center">
                            <input type="text" id="comment-input" class="form-control border-0 py-2" placeholder="Add comment...">
                            <button type="button" class="btn btn-light rounded-0 py-2 px-3 border-left" id="send-comment-btn" title="Send">
                                <i class="fas fa-paper-plane text-primary"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
        <?php } ?>
    </div>
</div>

<!-- Delete Comment Confirmation Modal -->
<div class="modal fade" id="deleteCommentModal" tabindex="-1" role="dialog" aria-labelledby="deleteCommentModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteCommentModalLabel">Delete Comment</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this comment?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteComment">Delete</button>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>

<!-- JavaScript for handling submission actions -->
<script>
$(document).ready(function() {
    // Mark as done button
    $('#mark-as-done-btn').click(function() {
        if (confirm('Mark this assignment as done?')) {
            $.ajax({
                url: 'ajax_handlers/mark_as_done.php',
                type: 'POST',
                data: {
                    activity_id: <?php echo $activityId; ?>,
                    action: 'mark_done'
                },
                success: function(response) {
                    try {
                        const result = JSON.parse(response);
                        if (result.success) {
                            // Update UI
                            $('#initial-actions').hide();
                            $('#turned-in-view').show();
                            $('#submission-status').removeClass('text-success text-danger').addClass('text-secondary').text('Turned in');

                            // Show success message
                            alert('Marked as done successfully!');
                        } else {
                            alert('Error: ' + result.message);
                        }
                    } catch (e) {
                        alert('An error occurred. Please try again.');
                        console.error(e, response);
                    }
                },
                error: function() {
                    alert('An error occurred. Please try again.');
                }
            });
        }
    });

    // Unsubmit button
    $('#unsubmit-btn').click(function() {
        if (confirm('Are you sure you want to unsubmit? This will remove your submission.')) {
            $.ajax({
                url: 'ajax_handlers/mark_as_done.php',
                type: 'POST',
                data: {
                    activity_id: <?php echo $activityId; ?>,
                    action: 'unsubmit'
                },
                success: function(response) {
                    try {
                        const result = JSON.parse(response);
                        if (result.success) {
                            // Update UI
                            $('#turned-in-view').hide();
                            $('#initial-actions').show();
                            $('#submission-status').removeClass('text-secondary text-danger').addClass('text-success').text('Assigned');

                            // Show success message
                            alert('Unsubmitted successfully!');
                        } else {
                            alert('Error: ' + result.message);
                        }
                    } catch (e) {
                        alert('An error occurred. Please try again.');
                        console.error(e, response);
                    }
                },
                error: function() {
                    alert('An error occurred. Please try again.');
                }
            });
        }
    });

    // View score button
    $('.view-score-btn').click(function(e) {
        e.preventDefault();
        $('#scoreModal').modal('show');
    });

    // Show comment input when link is clicked
    if (document.getElementById('add-comment-link')) {
        document.getElementById('add-comment-link').addEventListener('click', function(e) {
            e.preventDefault();
            document.getElementById('comment-input-container').style.display = 'block';
            document.getElementById('comment-input').focus();
        });
    }

    // Add comment functionality
    if (document.getElementById('send-comment-btn')) {
        document.getElementById('send-comment-btn').addEventListener('click', function() {
            const commentInput = document.getElementById('comment-input');
            const commentText = commentInput.value.trim();

            if (commentText !== '') {
                // Create a new comment object
                const now = new Date();
                const timeString = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

                // Get user profile picture
                <?php
                    $profilePic = '';
                    try {
                        $stmt = $pdo->prepare("SELECT profile_picture FROM users WHERE user_id = :userId");
                        $stmt->bindParam(':userId', $_SESSION['user_id']);
                        $stmt->execute();
                        if ($stmt->rowCount() > 0) {
                            $userData = $stmt->fetch();
                            if (!empty($userData['profile_picture'])) {
                                $profilePic = $userData['profile_picture'];
                            }
                        }
                    } catch (PDOException $e) {
                        // Silently fail and use default avatar
                    }
                ?>

                const comment = {
                    username: '<?php echo isset($_SESSION['first_name']) && isset($_SESSION['last_name']) ?
                        htmlspecialchars($_SESSION['first_name'] . ' ' . $_SESSION['last_name']) :
                        (isset($_SESSION['username']) ? htmlspecialchars($_SESSION['username']) : 'USER'); ?>',
                    content: commentText,
                    timestamp: timeString,
                    avatar: '<?php echo htmlspecialchars($profilePic); ?>',
                    date: now.toISOString()
                };

                // Save the comment to the server
                saveComment(comment);

                // Clear the input field and hide it
                commentInput.value = '';
                document.getElementById('comment-input-container').style.display = 'none';
            }
        });
    }

    // Function to save a comment
    function saveComment(comment) {
        // Create a form to submit the comment
        const formData = new FormData();
        formData.append('action', 'save_comment');
        formData.append('activity_id', '<?php echo $activityId; ?>');
        formData.append('comment', JSON.stringify(comment));

        // Send the comment to the server
        fetch('save_comment.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Add the comment to the UI
                addCommentToUI(comment, data.comment_id);
            } else {
                alert('Failed to save comment: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error saving comment:', error);

            // Even if the server save fails, show the comment in the UI
            // with a note that it's not saved
            addCommentToUI(comment, 'temp_' + Date.now());
        });
    }

    // Function to add a comment to the UI
    function addCommentToUI(comment, commentId) {
        const commentsContainer = document.getElementById('comments-container');

        // Check if there's a comment list already
        let commentList = commentsContainer.querySelector('.comment-list');
        if (!commentList) {
            commentList = document.createElement('div');
            commentList.className = 'comment-list';
            commentsContainer.appendChild(commentList);
        }

        // Create the comment element
        const commentElement = document.createElement('div');
        commentElement.className = 'comment-item mb-3';
        commentElement.dataset.commentId = commentId;

        // Create the comment HTML
        commentElement.innerHTML = `
            <div class="d-flex align-items-start">
                ${comment.avatar
                    ? `<img src="${comment.avatar}" class="rounded-circle mr-2" width="40" height="40" alt="User avatar" style="object-fit: cover;">`
                    : `<div class="rounded-circle mr-2 d-flex align-items-center justify-content-center bg-secondary text-white" style="width: 40px; height: 40px;">
                        ${comment.username.charAt(0).toUpperCase()}
                       </div>`
                }
                <div class="flex-grow-1">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong class="d-block">${comment.username}</strong>
                            <small class="text-muted">${comment.timestamp}</small>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-light rounded-circle" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="dropdown-menu dropdown-menu-right">
                                <a class="dropdown-item delete-comment" href="#" data-comment-id="${commentId}">Delete</a>
                            </div>
                        </div>
                    </div>
                    <p class="mb-0 mt-1">${formatCommentTextJS(comment.content)}</p>
                </div>
            </div>
        `;

        // Add the comment to the list
        commentList.appendChild(commentElement);

        // Add event listener for the delete button
        const deleteButton = commentElement.querySelector('.delete-comment');
        deleteButton.addEventListener('click', handleDeleteComment);
    }

    // Function to format comment text in JavaScript
    function formatCommentTextJS(text) {
        if (!text) return '';

        // Escape HTML
        text = text.replace(/&/g, '&amp;')
                   .replace(/</g, '&lt;')
                   .replace(/>/g, '&gt;')
                   .replace(/"/g, '&quot;')
                   .replace(/'/g, '&#039;');

        // Convert newlines to <br>
        text = text.replace(/\n/g, '<br>');

        return text;
    }

    // Handle delete comment button clicks
    function handleDeleteComment(e) {
        e.preventDefault();
        const commentId = this.dataset.commentId;

        // Store the comment ID to be deleted
        document.getElementById('confirmDeleteComment').dataset.commentId = commentId;

        // Show the confirmation modal
        $('#deleteCommentModal').modal('show');
    }

    // Add event listeners to existing delete buttons
    document.querySelectorAll('.delete-comment').forEach(button => {
        button.addEventListener('click', handleDeleteComment);
    });

    // Handle confirm delete button click
    if (document.getElementById('confirmDeleteComment')) {
        document.getElementById('confirmDeleteComment').addEventListener('click', function() {
            const commentId = this.dataset.commentId;

            // Create a form to submit the delete request
            const formData = new FormData();
            formData.append('action', 'delete_comment');
            formData.append('activity_id', '<?php echo $activityId; ?>');
            formData.append('comment_id', commentId);

            // Send the delete request to the server
            fetch('save_comment.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remove the comment from the UI
                    const commentElement = document.querySelector(`.comment-item[data-comment-id="${commentId}"]`);
                    if (commentElement) {
                        commentElement.remove();
                    }
                } else {
                    alert('Failed to delete comment: ' + data.message);
                }

                // Hide the modal
                $('#deleteCommentModal').modal('hide');
            })
            .catch(error => {
                console.error('Error deleting comment:', error);
                alert('An error occurred while deleting the comment.');

                // Hide the modal
                $('#deleteCommentModal').modal('hide');
            });
        });
    }

    // Allow pressing Enter to send comment
    if (document.getElementById('comment-input')) {
        document.getElementById('comment-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('send-comment-btn').click();
            }
        });
    }
});
</script>

<!-- Score Modal -->
<div class="modal fade" id="scoreModal" tabindex="-1" role="dialog" aria-labelledby="scoreModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="scoreModalLabel">Your Score</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <?php if ($studentSubmission): ?>
                    <div class="text-center">
                        <h3 class="mb-4">
                            <?php if ($studentSubmission['grade'] !== null): ?>
                                <span class="text-success">
                                    <?php
                                    // Calculate actual points earned based on percentage grade
                                    $earnedPoints = ($studentSubmission['grade'] / 100) * $totalActivityPoints;
                                    echo round($earnedPoints) . '/' . $totalActivityPoints;
                                    ?>
                                </span>
                            <?php else: ?>
                                <span class="text-warning">Not yet graded</span>
                            <?php endif; ?>
                        </h3>

                        <p class="mb-3">Submitted on: <?php echo date('M j, Y g:i A', strtotime($studentSubmission['submission_date'])); ?></p>

                        <?php if (!empty($studentSubmission['feedback'])): ?>
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">Instructor Feedback</h6>
                            </div>
                            <div class="card-body">
                                <?php echo nl2br(htmlspecialchars($studentSubmission['feedback'])); ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="alert alert-warning">
                        You haven't submitted this assignment yet.
                    </div>
                <?php endif; ?>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
