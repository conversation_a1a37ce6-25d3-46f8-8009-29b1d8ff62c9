<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/activity_functions.php';
require_once 'includes/utility_functions.php';
require_once 'includes/submission_functions.php';

// Check if the user is logged in, if not redirect to login page
if (!isLoggedIn()) {
    header("location: login.php");
    exit;
}

// Check if activity ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("location: index.php");
    exit;
}

$activityId = intval($_GET['id']);

// Get activity details
$activity = getActivityById($activityId);

// Check if activity exists
if (is_string($activity)) {
    $_SESSION['error'] = $activity;
    header("location: index.php");
    exit;
}

// Get course details
$courseId = $activity['course_id'];
$course = getCourseById($courseId);

// Check if course exists
if (is_string($course)) {
    $_SESSION['error'] = $course;
    header("location: index.php");
    exit;
}

// Check if user has access to this activity
$hasAccess = false;

if (isAdmin()) {
    $hasAccess = true;
} elseif (isTeacher()) {
    // Teachers can access if they created the course or are instructors
    if ($course['created_by'] == $_SESSION['user_id'] || isInstructor($_SESSION['user_id'], $courseId)) {
        $hasAccess = true;
    }
} elseif (isStudent()) {
    // Students can access if they are enrolled and the activity is published
    if (isEnrolled($_SESSION['user_id'], $courseId) && $activity['is_published']) {
        $hasAccess = true;
    }
}

if (!$hasAccess) {
    $_SESSION['error'] = "You do not have permission to view this activity.";
    header("location: course_view_full.php?id=$courseId");
    exit;
}

// Check if this is a quiz, activity, or assignment
$isQuiz = ($activity['activity_type'] == 'quiz');
$isActivity = ($activity['activity_type'] == 'activity');
$isAssignment = ($activity['activity_type'] == 'assignment');
$quizQuestions = [];
$activityQuestions = [];
$assignmentQuestions = [];

if ($isQuiz) {
    $quizQuestions = getQuizQuestions($activityId);
    if (is_string($quizQuestions)) {
        $quizQuestions = [];
    }
}

if ($isActivity || $isAssignment) {
    // Both activities and assignments use the same function for consistency
    $activityQuestions = getActivityQuestions($activityId);
    if (is_string($activityQuestions)) {
        $activityQuestions = [];
    }
}

// Check if the student has already submitted this activity
$studentSubmission = null;
if (isStudent()) {
    // Try to get submission from activity_submissions table first
    $result = getStudentActivitySubmission($activityId, $_SESSION['user_id']);
    if (!is_string($result)) {
        $studentSubmission = $result;
    } else {
        // If not found, try the submissions table
        $result = getStudentSubmission($activityId, $_SESSION['user_id']);
        if (!is_string($result)) {
            $studentSubmission = $result;
        }
    }
}

// Get all submissions (for teachers)
$submissions = [];
if (isAdmin() || isTeacher()) {
    $result = getActivitySubmissions($activityId);
    if (!is_string($result)) {
        $submissions = $result;
    }
}

// Calculate total points for this activity based on questions
$totalActivityPoints = 0;
if ($isQuiz && !empty($quizQuestions)) {
    foreach ($quizQuestions as $question) {
        $totalActivityPoints += $question['points'];
    }
} elseif (($isActivity || $isAssignment) && !empty($activityQuestions)) {
    foreach ($activityQuestions as $question) {
        $totalActivityPoints += $question['points'];
    }
}

// For assignments without questions, use a default of 100 points
if ($isAssignment && $totalActivityPoints == 0) {
    $totalActivityPoints = 100;
}

// Special case handling for specific activities
// Activity ID 7 should have 3 points
if ($activityId == 7) {
    $totalActivityPoints = 3;
}

// Set page title
$page_title = htmlspecialchars($activity['title']);

// Include header
require_once 'includes/header.php';
?>

<div class="container mt-4">
    <div class="row">
        <!-- Main content area -->
        <div class="col-md-9">
            <nav aria-label="breadcrumb" class="mt-3">
                <ol class="breadcrumb bg-transparent px-0">
                    <li class="breadcrumb-item"><a href="index.php">Home</a></li>
                    <li class="breadcrumb-item"><a href="course_view_full.php?id=<?php echo $courseId; ?>"><?php echo htmlspecialchars($course['title']); ?></a></li>
                    <li class="breadcrumb-item active"><?php echo htmlspecialchars($activity['title']); ?></li>
                </ol>
            </nav>

            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success"><?php echo $_SESSION['success']; unset($_SESSION['success']); ?></div>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger"><?php echo $_SESSION['error']; unset($_SESSION['error']); ?></div>
            <?php endif; ?>

            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h1 class="h3 mb-0"><?php echo htmlspecialchars($activity['title']); ?></h1>

                    <?php if (isAdmin() || (isTeacher() && ($course['created_by'] == $_SESSION['user_id'] || isInstructor($_SESSION['user_id'], $courseId)))): ?>
                    <div class="btn-group">
                        <?php if ($activity['activity_type'] == 'quiz'): ?>
                        <a href="quiz_edit.php?id=<?php echo $activityId; ?>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <?php elseif ($activity['activity_type'] == 'assignment'): ?>
                        <a href="assignment_edit.php?id=<?php echo $activityId; ?>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <?php elseif ($activity['activity_type'] == 'material'): ?>
                        <a href="material_edit.php?id=<?php echo $activityId; ?>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <?php else: ?>
                        <a href="activity_edit.php?id=<?php echo $activityId; ?>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <?php endif; ?>
                        <a href="activity_action.php?action=delete&id=<?php echo $activityId; ?>" class="btn btn-outline-danger btn-sm" onclick="return confirm('Are you sure you want to delete this activity? This action cannot be undone.');">
                            <i class="fas fa-trash"></i> Delete
                        </a>
                    </div>
                    <?php endif; ?>
                </div>

                <div class="card-body">
                    <?php if (isset($_GET['success']) && $_GET['success'] == 1): ?>
                    <div class="alert alert-success">
                        Your answers have been submitted successfully!
                    </div>
                    <?php endif; ?>

                    <div class="activity-meta mb-3">
                        <span class="badge badge-<?php echo $activity['is_published'] ? 'success' : 'warning'; ?>">
                            <?php echo $activity['is_published'] ? 'Published' : 'Draft'; ?>
                        </span>

                        <span class="badge badge-info">
                            <?php
                            switch($activity['activity_type']) {
                                case 'material': echo 'Material'; break;
                                case 'assignment': echo 'Assignment'; break;
                                case 'quiz': echo 'Quiz'; break;
                                case 'question': echo 'Question'; break;
                                default: echo ucfirst($activity['activity_type']); break;
                            }
                            ?>
                        </span>

                        <?php if ($activity['activity_type'] == 'assignment' || $activity['activity_type'] == 'quiz'): ?>
                        <span class="badge badge-primary"><?php echo $activity['points']; ?> points</span>
                        <?php endif; ?>

                        <?php if (!empty($activity['due_date'])): ?>
                        <span class="badge badge-secondary">
                            Due: <?php echo date('M j, Y g:i A', strtotime($activity['due_date'])); ?>
                        </span>
                        <?php endif; ?>
                    </div>

                    <div class="activity-description mb-4">
                        <?php echo nl2br(htmlspecialchars($activity['description'])); ?>
                    </div>

                    <?php if ($activity['activity_type'] == 'quiz' && !empty($quizQuestions)) { ?>
                        <?php if (isAdmin() || isTeacher() || !$studentSubmission) { ?>
                    <div class="quiz-preview mb-4">
                        <h4>Quiz Questions</h4>
                        <div class="list-group">
                            <?php foreach ($quizQuestions as $index => $question) { ?>
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="mb-1">Question <?php echo $index + 1; ?> (<?php echo $question['points']; ?> pts)</h5>
                                    <span class="badge badge-secondary"><?php echo ucfirst(str_replace('_', ' ', $question['question_type'])); ?></span>
                                </div>
                                <p class="mb-1"><?php echo htmlspecialchars($question['question_text']); ?></p>

                                <?php
                                // Handle different question types
                                if ($question['question_type'] == 'short_answer') {
                                    // For short answer questions
                                    ?>
                                    <div class="card mb-3 ml-4">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">Short Answer</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="form-group mb-0">
                                                <input type="text" class="form-control" placeholder="<?php echo isStudent() ? 'Type your answer here' : 'Student will type answer here'; ?>" <?php echo isStudent() ? '' : 'readonly'; ?>>
                                                <?php if (isAdmin() || isTeacher()) { ?>
                                                <small class="form-text text-muted mt-2">
                                                    <i class="fas fa-info-circle mr-1"></i> Students will enter their answer in a text field like this one.
                                                </small>
                                                <?php } ?>
                                            </div>
                                        </div>
                                    </div>
                                    <?php
                                    // Show answer options for short answer questions only to admins and teachers
                                    if ((isAdmin() || isTeacher()) && isset($question['options']) && count($question['options']) > 0) {
                                    ?>
                                    <div class="card mb-3 ml-4">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">Answer Options (Only visible to instructors)</h6>
                                        </div>
                                        <div class="list-group list-group-flush">
                                            <?php foreach ($question['options'] as $optionIndex => $option) { ?>
                                            <div class="list-group-item <?php echo $option['is_correct'] ? 'bg-light' : ''; ?>">
                                                <div class="d-flex align-items-center">
                                                    <i class="far fa-edit mr-3"></i>
                                                    <div class="option-text <?php echo $option['is_correct'] ? 'font-weight-bold' : ''; ?>">
                                                        <?php echo htmlspecialchars($option['option_text']); ?>
                                                    </div>
                                                    <?php if ($option['is_correct']) { ?>
                                                    <div class="ml-auto">
                                                        <span class="badge badge-success">Correct Answer</span>
                                                    </div>
                                                    <?php } ?>
                                                </div>
                                            </div>
                                            <?php } // end foreach ?>
                                        </div>
                                    </div>
                                    <?php } // end if ?>
                                <?php
                                } else {
                                    // For other question types (multiple choice, true/false, etc.)
                                    if (isset($question['options']) && count($question['options']) > 0) {
                                ?>
                                    <div class="card mb-3 ml-4">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">Answer Options</h6>
                                        </div>
                                        <div class="list-group list-group-flush">
                                            <?php foreach ($question['options'] as $optionIndex => $option) { ?>
                                            <div class="list-group-item <?php echo (isAdmin() || isTeacher()) && $option['is_correct'] ? 'bg-light' : ''; ?> option-container"
                                                 data-question-id="<?php echo $question['question_id']; ?>"
                                                 data-option-id="<?php echo $option['option_id']; ?>">
                                                <div class="d-flex align-items-center">
                                                    <?php if ($question['question_type'] == 'multiple_choice') { ?>
                                                        <div class="option-indicator mr-3">
                                                            <span class="badge badge-pill badge-<?php echo (isAdmin() || isTeacher()) && $option['is_correct'] ? 'success' : 'secondary'; ?>"><?php echo chr(65 + $optionIndex); ?></span>
                                                        </div>
                                                    <?php } elseif ($question['question_type'] == 'checkbox') { ?>
                                                        <div class="option-indicator mr-3">
                                                            <i class="far fa-<?php echo (isAdmin() || isTeacher()) && $option['is_correct'] ? 'check-' : ''; ?>square"></i>
                                                        </div>
                                                    <?php } elseif ($question['question_type'] == 'true_false') { ?>
                                                        <div class="option-indicator mr-3">
                                                            <span class="badge badge-pill badge-<?php echo (isAdmin() || isTeacher()) && $option['is_correct'] ? 'success' : 'secondary'; ?>"><?php echo $option['option_text']; ?></span>
                                                        </div>
                                                    <?php } ?>

                                                    <div class="option-text <?php echo (isAdmin() || isTeacher()) && $option['is_correct'] ? 'font-weight-bold' : ''; ?>">
                                                        <?php echo htmlspecialchars($option['option_text']); ?>
                                                    </div>

                                                    <?php if ((isAdmin() || isTeacher()) && $option['is_correct']) { ?>
                                                    <div class="ml-auto">
                                                        <span class="badge badge-success">Correct Answer</span>
                                                    </div>
                                                    <?php } ?>
                                                </div>
                                            </div>
                                            <?php } // end foreach ?>
                                        </div>
                                    </div>
                                <?php
                                    }
                                }
                                ?>
                            </div>
                            <?php } // end foreach ?>
                        </div>
                    </div>
                    <?php } // end if ?>
                    <?php } // end if ?>

                    <?php if (($activity['activity_type'] == 'activity' || $activity['activity_type'] == 'assignment') && !empty($activityQuestions)) { ?>
                        <?php if (isAdmin() || isTeacher() || !$studentSubmission) { ?>
                    <div class="activity-preview mb-4">
                        <h4><?php echo ucfirst($activity['activity_type']); ?> Questions</h4>
                        <div class="list-group">
                            <?php
                            // Both activities and assignments use activityQuestions
                            foreach ($activityQuestions as $index => $question):
                            ?>
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="mb-1">Question <?php echo $index + 1; ?> (<?php echo $question['points']; ?> pts)</h5>
                                    <span class="badge badge-secondary"><?php echo ucfirst(str_replace('_', ' ', $question['question_type'])); ?></span>
                                </div>
                                <p class="mb-1"><?php echo htmlspecialchars($question['question_text']); ?></p>

                                <?php
                                // Handle different question types
                                if ($question['question_type'] == 'short_answer') {
                                    // For short answer questions
                                    ?>
                                    <div class="card mb-3 ml-4">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">Short Answer</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="form-group mb-0">
                                                <input type="text" class="form-control" placeholder="<?php echo isStudent() ? 'Type your answer here' : 'Student will type answer here'; ?>" <?php echo isStudent() ? '' : 'readonly'; ?>>
                                                <?php if (isAdmin() || isTeacher()) { ?>
                                                <small class="form-text text-muted mt-2">
                                                    <i class="fas fa-info-circle mr-1"></i> Students will enter their answer in a text field like this one.
                                                </small>
                                                <?php } ?>
                                            </div>
                                        </div>
                                    </div>
                                    <?php
                                    // Show answer options for short answer questions only to admins and teachers
                                    if ((isAdmin() || isTeacher()) && isset($question['options']) && count($question['options']) > 0) {
                                    ?>
                                    <div class="card mb-3 ml-4">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">Answer Options (Only visible to instructors)</h6>
                                        </div>
                                        <div class="list-group list-group-flush">
                                            <?php foreach ($question['options'] as $optionIndex => $option) { ?>
                                            <div class="list-group-item <?php echo $option['is_correct'] ? 'bg-light' : ''; ?>">
                                                <div class="d-flex align-items-center">
                                                    <i class="far fa-edit mr-3"></i>
                                                    <div class="option-text <?php echo $option['is_correct'] ? 'font-weight-bold' : ''; ?>">
                                                        <?php echo htmlspecialchars($option['option_text']); ?>
                                                    </div>
                                                    <?php if ($option['is_correct']) { ?>
                                                    <div class="ml-auto">
                                                        <span class="badge badge-success">Correct Answer</span>
                                                    </div>
                                                    <?php } ?>
                                                </div>
                                            </div>
                                            <?php } // end foreach ?>
                                        </div>
                                    </div>
                                    <?php } // end if ?>
                                <?php
                                } else {
                                    // For other question types (multiple choice, true/false, etc.)
                                    if (isset($question['options']) && count($question['options']) > 0) {
                                ?>
                                    <div class="card mb-3 ml-4">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">Answer Options</h6>
                                        </div>
                                        <div class="list-group list-group-flush">
                                            <?php foreach ($question['options'] as $optionIndex => $option) { ?>
                                            <div class="list-group-item <?php echo (isAdmin() || isTeacher()) && $option['is_correct'] ? 'bg-light' : ''; ?> option-container"
                                                 data-question-id="<?php echo $question['question_id']; ?>"
                                                 data-option-id="<?php echo $option['option_id']; ?>">
                                                <div class="d-flex align-items-center">
                                                    <?php if ($question['question_type'] == 'multiple_choice') { ?>
                                                        <div class="option-indicator mr-3">
                                                            <span class="badge badge-pill badge-<?php echo (isAdmin() || isTeacher()) && $option['is_correct'] ? 'success' : 'secondary'; ?>"><?php echo chr(65 + $optionIndex); ?></span>
                                                        </div>
                                                    <?php } elseif ($question['question_type'] == 'true_false') { ?>
                                                        <div class="option-indicator mr-3">
                                                            <span class="badge badge-pill badge-<?php echo (isAdmin() || isTeacher()) && $option['is_correct'] ? 'success' : 'secondary'; ?>"><?php echo $option['option_text']; ?></span>
                                                        </div>
                                                    <?php } ?>

                                                    <div class="option-text <?php echo (isAdmin() || isTeacher()) && $option['is_correct'] ? 'font-weight-bold' : ''; ?>">
                                                        <?php echo htmlspecialchars($option['option_text']); ?>
                                                    </div>

                                                    <?php if ((isAdmin() || isTeacher()) && $option['is_correct']) { ?>
                                                    <div class="ml-auto">
                                                        <span class="badge badge-success">Correct Answer</span>
                                                    </div>
                                                    <?php } ?>
                                                </div>
                                            </div>
                                            <?php } // end foreach ?>
                                        </div>
                                    </div>
                                <?php
                                    }
                                }
                                ?>
                            </div>
                            <?php } // end foreach ?>
                        </div>
                    </div>
                    <?php endif; ?>
                    <?php endif; ?>

                    <?php if (isStudent() && ($activity['activity_type'] == 'assignment' || $activity['activity_type'] == 'quiz' || $activity['activity_type'] == 'activity')): ?>
                    <div class="student-actions mt-4">
                        <?php if ($studentSubmission): ?>
                        <!-- Already submitted view - only show this message, no questionnaire -->
                        <div class="alert alert-info">
                            <h5><i class="fas fa-check-circle"></i> Already Submitted</h5>
                            <p>Submitted on: <?php echo date('M j, Y g:i A', strtotime($studentSubmission['submission_date'])); ?></p>

                            <?php if (isset($studentSubmission['is_late']) && $studentSubmission['is_late']): ?>
                            <p><span class="badge badge-warning">Late Submission</span></p>
                            <?php endif; ?>

                            <?php if ($studentSubmission['grade'] !== null): ?>
                            <p>Score: <?php
                                // Calculate actual points earned based on percentage grade
                                $earnedPoints = ($studentSubmission['grade'] / 100) * $totalActivityPoints;
                                echo round($earnedPoints) . '/' . $totalActivityPoints;
                            ?></p>
                            <?php if (!empty($studentSubmission['feedback'])): ?>
                            <p>Feedback: <?php echo nl2br(htmlspecialchars($studentSubmission['feedback'])); ?></p>
                            <?php endif; ?>
                            <?php else: ?>
                            <p>Status: Not yet graded</p>
                            <?php endif; ?>

                            <?php if ($activity['activity_type'] == 'assignment' && isset($studentSubmission['file_name'])): ?>
                            <a href="easy_submit.php?id=<?php echo $activityId; ?>" class="btn btn-primary">View/Edit Submission</a>
                            <?php else: ?>
                            <a href="#" class="btn btn-primary view-score-btn">View Score</a>
                            <?php endif; ?>
                        </div>
                        <?php else: ?>
                        <!-- Not submitted yet view -->
                        <?php
                            $canSubmit = true;
                            $disabledReason = "";

                            // Check if due date has passed and late submissions are not allowed
                            if (!empty($activity['due_date'])) {
                                $dueDate = new DateTime($activity['due_date']);
                                $now = new DateTime();

                                if ($now > $dueDate) {
                                    if (!$activity['allow_late_submissions']) {
                                        $canSubmit = false;
                                        $disabledReason = "Due date has passed and late submissions are not allowed.";
                                    } else {
                                        $disabledReason = "This submission will be marked as late.";
                                    }
                                }
                            }
                        ?>

                        <?php if ($canSubmit): ?>
                        <!-- Links to take/submit assessments -->
                        <div class="assessment-actions">
                            <?php if ($activity['activity_type'] == 'quiz'): ?>
                                <a href="quiz_take.php?id=<?php echo $activityId; ?>" class="btn btn-primary btn-lg">
                                    <i class="fas fa-edit mr-2"></i> Take Quiz
                                </a>
                            <?php elseif ($activity['activity_type'] == 'assignment'): ?>
                                <a href="easy_submit.php?id=<?php echo $activityId; ?>" class="btn btn-primary btn-lg">
                                    <i class="fas fa-edit mr-2"></i> Submit
                                </a>
                            <?php elseif ($activity['activity_type'] == 'activity'): ?>
                                <a href="easy_submit.php?id=<?php echo $activityId; ?>" class="btn btn-primary btn-lg">
                                    <i class="fas fa-edit mr-2"></i> Submit Activity
                                </a>
                            <?php endif; ?>

                            <?php if (!empty($disabledReason)): ?>
                                <small class="d-block text-warning mt-2"><?php echo htmlspecialchars($disabledReason); ?></small>
                            <?php endif; ?>
                        </div>
                        <?php else: ?>
                        <button class="btn btn-secondary btn-lg" disabled title="<?php echo htmlspecialchars($disabledReason); ?>">
                            <?php
                            if ($activity['activity_type'] == 'quiz') {
                                echo '<i class="fas fa-lock mr-2"></i> Quiz Closed';
                            } elseif ($activity['activity_type'] == 'activity') {
                                echo '<i class="fas fa-lock mr-2"></i> Activity Closed';
                            } else {
                                echo '<i class="fas fa-lock mr-2"></i> Assignment Closed';
                            }
                            ?>
                        </button>
                        <small class="d-block text-danger mt-2"><?php echo htmlspecialchars($disabledReason); ?></small>
                        <?php endif; ?>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>

                    <?php if ((isAdmin() || isTeacher()) && !empty($submissions) && $activity['activity_type'] != 'material'): ?>
                    <div class="teacher-view mt-4">
                        <h4>Student Submissions</h4>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Student</th>
                                        <th>Submission Date</th>
                                        <th>Status</th>
                                        <th>Points</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($submissions as $submission): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($submission['first_name'] . ' ' . $submission['last_name']); ?></td>
                                        <td><?php echo date('M j, Y g:i A', strtotime($submission['submission_date'])); ?></td>
                                        <td>
                                            <?php if ($submission['grade'] !== null): ?>
                                            <span class="badge badge-success">Graded</span>
                                            <?php else: ?>
                                            <span class="badge badge-warning">Not Graded</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($submission['grade'] !== null): ?>
                                            <?php
                                                // Calculate actual points earned based on percentage grade
                                                $earnedPoints = ($submission['grade'] / 100) * $totalActivityPoints;
                                                echo round($earnedPoints) . '/' . $totalActivityPoints;
                                            ?>
                                            <?php else: ?>
                                            -
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <a href="submission_view.php?id=<?php echo $submission['submission_id']; ?>" class="btn btn-sm btn-primary">View</a>
                                            <a href="submission_grade.php?id=<?php echo $submission['submission_id']; ?>" class="btn btn-sm btn-info">Grade</a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <div class="card-footer text-muted">
                    Created by <?php echo htmlspecialchars($activity['creator_name']); ?> on <?php echo date('M j, Y', strtotime($activity['created_at'])); ?>
                </div>
            </div>

            <div class="text-center mb-4">
                <a href="course_view_full.php?id=<?php echo $courseId; ?>" class="btn btn-secondary">Back to Course</a>
            </div>
        </div>

        <!-- Right sidebar for Your work and Private comments -->
        <?php if (isStudent()): ?>
        <div class="col-md-3">
            <?php if ($activity['activity_type'] != 'material'): ?>
            <!-- Your work section -->
            <div class="card mb-4 shadow-sm">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="card-title mb-0">Your work</h5>
                        <?php
                        // Check if due date has passed without submission
                        $statusClass = 'text-success';
                        $statusText = 'Assigned';
                        $dueDatePassed = false;

                        if ($studentSubmission) {
                            $statusClass = 'text-secondary';
                            $statusText = 'Turned in';
                        } else if (!empty($activity['due_date'])) {
                            $dueDate = new DateTime($activity['due_date']);
                            $now = new DateTime();

                            if ($now > $dueDate) {
                                $statusClass = 'text-danger';
                                $statusText = 'Missing';
                                $dueDatePassed = true;
                            }
                        }
                        ?>
                        <span class="<?php echo $statusClass; ?>" id="submission-status">
                            <?php echo $statusText; ?>
                        </span>
                    </div>

                    <!-- Initial actions (visible by default if not submitted) -->
                    <div id="initial-actions" style="<?php echo $studentSubmission ? 'display: none;' : ''; ?>">
                        <!-- Mark as done button -->
                        <button type="button" class="btn btn-primary btn-block" id="mark-as-done-btn">
                            Mark as done
                        </button>
                    </div>

                    <!-- Turned in view (visible when submitted) -->
                    <div id="turned-in-view" style="<?php echo $studentSubmission ? '' : 'display: none;'; ?>">
                        <div class="d-flex align-items-center mb-3">
                            <i class="fas fa-check-circle text-success mr-2"></i>
                            <span id="attachment-status">
                                <?php if ($studentSubmission && !empty($studentSubmission['file_path'])): ?>
                                    <?php echo basename($studentSubmission['file_path']); ?>
                                <?php elseif ($studentSubmission && !empty($studentSubmission['submission_link'])): ?>
                                    Link: <?php echo $studentSubmission['submission_link']; ?>
                                <?php else: ?>
                                    Marked as done
                                <?php endif; ?>
                            </span>
                        </div>
                        <button type="button" class="btn btn-outline-secondary btn-block" id="unsubmit-btn">
                            Unsubmit
                        </button>
                    </div>


                </div>
            </div>
            <?php endif; ?>

            <!-- Private comments section -->
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <h5 class="card-title mb-0" id="comments-title">Private comments</h5>
                    </div>
                    <div id="comments-container">
                        <!-- Comments will be loaded here -->
                        <?php
                        // Check if there are any saved comments for this activity
                        $comments = [];
                        $commentsFile = 'data/comments_' . $activityId . '.json';
                        if (file_exists($commentsFile)) {
                            $comments = json_decode(file_get_contents($commentsFile), true);
                        }

                        // Function to get user profile picture by username
                        function getUserProfilePicture($pdo, $username) {
                            try {
                                // Extract first and last name from the username
                                $nameParts = explode(' ', $username, 2);
                                if (count($nameParts) == 2) {
                                    $firstName = $nameParts[0];
                                    $lastName = $nameParts[1];

                                    $stmt = $pdo->prepare("SELECT profile_picture FROM users WHERE first_name = :firstName AND last_name = :lastName LIMIT 1");
                                    $stmt->bindParam(':firstName', $firstName);
                                    $stmt->bindParam(':lastName', $lastName);
                                    $stmt->execute();

                                    if ($stmt->rowCount() > 0) {
                                        $userData = $stmt->fetch();
                                        if (!empty($userData['profile_picture'])) {
                                            return $userData['profile_picture'];
                                        }
                                    }
                                }
                            } catch (PDOException $e) {
                                // Silently fail and use default avatar
                            }
                            return '';
                        }

                        if (!empty($comments)):
                        ?>
                            <div class="comment-list">
                                <?php foreach ($comments as $index => $comment):
                                    // Try to get the profile picture if it's not already set
                                    $profilePicture = !empty($comment['avatar']) ? $comment['avatar'] : getUserProfilePicture($pdo, $comment['username']);
                                ?>
                                <div class="comment-item mb-3" data-comment-id="<?php echo $index; ?>">
                                    <div class="d-flex align-items-start">
                                        <?php if (!empty($profilePicture)): ?>
                                            <img src="<?php echo htmlspecialchars($profilePicture); ?>" class="rounded-circle mr-2" width="40" height="40" alt="User avatar" style="object-fit: cover;">
                                        <?php else: ?>
                                            <div class="rounded-circle mr-2 d-flex align-items-center justify-content-center bg-secondary text-white" style="width: 40px; height: 40px;">
                                                <?php echo strtoupper(substr($comment['username'], 0, 1)); ?>
                                            </div>
                                        <?php endif; ?>
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <strong class="d-block"><?php echo htmlspecialchars($comment['username']); ?></strong>
                                                    <small class="text-muted"><?php echo htmlspecialchars($comment['timestamp']); ?></small>
                                                </div>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-light rounded-circle" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                    <div class="dropdown-menu dropdown-menu-right">
                                                        <a class="dropdown-item delete-comment" href="#" data-comment-id="<?php echo $index; ?>">Delete</a>
                                                    </div>
                                                </div>
                                            </div>
                                            <p class="mb-0 mt-1"><?php echo formatCommentText(htmlspecialchars($comment['content'])); ?></p>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Comment link (always visible) -->
                    <div class="mt-3">
                        <a href="#" class="text-primary" id="add-comment-link">
                            Add comment to <?php echo htmlspecialchars($activity['creator_name']); ?>
                        </a>
                    </div>

                    <!-- Comment input field (hidden initially) -->
                    <div id="comment-input-container" class="comment-input-container border rounded p-0 mt-2" style="display: none;">
                        <div class="d-flex align-items-center">
                            <input type="text" id="comment-input" class="form-control border-0 py-2" placeholder="Add comment...">
                            <button type="button" class="btn btn-light rounded-0 py-2 px-3 border-left" id="send-comment-btn" title="Send">
                                <i class="fas fa-paper-plane text-primary"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Delete Comment Confirmation Modal -->
            <div class="modal fade" id="deleteCommentModal" tabindex="-1" role="dialog" aria-labelledby="deleteCommentModalLabel" aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="deleteCommentModalLabel">Delete Comment</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            Are you sure you want to delete this comment?
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-danger" id="confirmDeleteComment">Delete</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Score Modal -->
            <div class="modal fade" id="scoreModal" tabindex="-1" role="dialog" aria-labelledby="scoreModalLabel" aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="scoreModalLabel">Your Score</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <?php if ($studentSubmission): ?>
                                <div class="text-center">
                                    <h3 class="mb-4">
                                        <?php if ($studentSubmission['grade'] !== null): ?>
                                            <span class="text-success">
                                                <?php
                                                // Calculate actual points earned based on percentage grade
                                                $earnedPoints = ($studentSubmission['grade'] / 100) * $totalActivityPoints;
                                                echo round($earnedPoints) . '/' . $totalActivityPoints;
                                                ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-warning">Not yet graded</span>
                                        <?php endif; ?>
                                    </h3>

                                    <p class="mb-3">Submitted on: <?php echo date('M j, Y g:i A', strtotime($studentSubmission['submission_date'])); ?></p>

                                    <?php if (isset($studentSubmission['is_late']) && $studentSubmission['is_late']): ?>
                                        <p><span class="badge badge-warning">Late Submission</span></p>
                                    <?php endif; ?>

                                    <?php if (!empty($studentSubmission['feedback'])): ?>
                                        <div class="card mt-3">
                                            <div class="card-header">Instructor Feedback</div>
                                            <div class="card-body">
                                                <?php echo nl2br(htmlspecialchars($studentSubmission['feedback'])); ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-primary" data-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>

<style>
/* Option styling for multiple choice and true/false questions */
.option-circle {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid #ccc;
    display: flex;
    align-items: center;
    justify-content: center;
}

.option-circle.selected {
    border-color: #007bff;
}

.inner-circle {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: transparent;
    transition: background-color 0.2s ease;
}

.option-circle.selected .inner-circle {
    background-color: #007bff;
}

.selected-option {
    background-color: #e3f2fd;
    border-color: #007bff !important;
}

.option-container:hover {
    background-color: #f8f9fa;
    border-color: #007bff !important;
}

.option-container {
    transition: all 0.2s ease;
    cursor: pointer;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap dropdowns
    $('.dropdown-toggle').dropdown();



    // Make quiz/assignment options clickable and selectable
    const optionElements = document.querySelectorAll('.option');
    const selectedAnswers = {};

    optionElements.forEach(option => {
        option.style.cursor = 'pointer';
        option.addEventListener('click', function() {
            <?php if (isStudent()): ?>
            const questionId = this.getAttribute('data-question-id');
            const optionId = this.getAttribute('data-option-id');

            if (questionId && optionId) {
                // Deselect all options for this question
                document.querySelectorAll(`.option[data-question-id="${questionId}"]`).forEach(opt => {
                    opt.classList.remove('selected-option');
                    const circle = opt.querySelector('.option-circle');
                    if (circle) circle.classList.remove('selected');
                });

                // Select this option
                this.classList.add('selected-option');
                const circle = this.querySelector('.option-circle');
                if (circle) circle.classList.add('selected');

                // Store the selected answer
                selectedAnswers[questionId] = optionId;

                // Update hidden inputs for form submission
                updateSelectedAnswersInputs();
            }
            <?php endif; ?>
        });
    });

    // Function to update hidden inputs for form submission
    function updateSelectedAnswersInputs() {
        const container = document.getElementById('selected-answers-container');
        if (!container) return;

        // Clear existing inputs
        container.innerHTML = '';

        // Add new inputs for each selected answer
        for (const questionId in selectedAnswers) {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = `answer_${questionId}`;
            input.value = selectedAnswers[questionId];
            container.appendChild(input);
        }
    }

    // Submit answers button functionality
    if (document.getElementById('submit-answers-btn')) {
        document.getElementById('submit-answers-btn').addEventListener('click', function() {
            // Get the form data
            const form = document.getElementById('direct-submission-form');
            const formData = new FormData(form);

            // Add selected answers to form data
            for (const questionId in selectedAnswers) {
                formData.append(`answer_${questionId}`, selectedAnswers[questionId]);
            }

            // Send the form data to the server
            fetch('activity_submit_handler.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (data.redirect) {
                        // Redirect to the specified URL
                        window.location.href = data.redirect;
                    } else {
                        // Show success message
                        const successMessage = document.createElement('div');
                        successMessage.className = 'alert alert-success mt-3';
                        successMessage.textContent = 'Answers submitted successfully!';
                        document.querySelector('.card-body').appendChild(successMessage);

                        // Refresh the page after 1 second
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    }
                } else {
                    alert('Failed to submit answers: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error submitting answers:', error);
                alert('An error occurred while submitting your answers. Please try again.');
            });
        });
    }



    // Mark as done button functionality
    if (document.getElementById('mark-as-done-btn')) {
        document.getElementById('mark-as-done-btn').addEventListener('click', function() {
            // Check if due date has passed
            <?php if (!empty($activity['due_date'])): ?>
            const dueDate = new Date('<?php echo date('c', strtotime($activity['due_date'])); ?>');
            const now = new Date();

            if (now > dueDate && !<?php echo $activity['allow_late_submissions'] ? 'true' : 'false'; ?>) {
                alert('The due date for this activity has passed. You cannot mark it as done.');
                return;
            }
            <?php endif; ?>

            // Create a form to submit the "mark as done" action
            const formData = new FormData();
            formData.append('action', 'mark_as_done');
            formData.append('activity_id', '<?php echo $activityId; ?>');

            // Send the action to the server
            fetch('activity_submit_handler.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Hide initial view and show turned-in view
                    document.getElementById('initial-actions').style.display = 'none';
                    document.getElementById('turned-in-view').style.display = 'block';

                    // Update status to "Turned in"
                    document.getElementById('submission-status').textContent = 'Turned in';
                    document.getElementById('submission-status').className = 'text-secondary';

                    // Update attachment status
                    document.getElementById('attachment-status').textContent = 'Marked as done';

                    // Show success message
                    const successMessage = document.createElement('div');
                    successMessage.className = 'alert alert-success mt-3';
                    successMessage.textContent = 'Activity marked as done successfully!';
                    document.querySelector('.card-body').insertBefore(successMessage, document.getElementById('turned-in-view'));

                    // Remove the message after 3 seconds
                    setTimeout(() => {
                        successMessage.remove();
                    }, 3000);

                    // Refresh the page after 1 second to show the updated status
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    alert('Failed to mark as done: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error marking as done:', error);
                alert('An error occurred while marking as done. Please try again.');
            });
        });
    }

    // Unsubmit button functionality
    if (document.getElementById('unsubmit-btn')) {
        document.getElementById('unsubmit-btn').addEventListener('click', function() {
            // Create a form to submit the "unsubmit" action
            const formData = new FormData();
            formData.append('action', 'unsubmit');
            formData.append('activity_id', '<?php echo $activityId; ?>');

            // Send the action to the server
            fetch('activity_submit_handler.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show initial view and hide turned-in view
                    document.getElementById('initial-actions').style.display = 'block';
                    document.getElementById('turned-in-view').style.display = 'none';

                    // Reset status to "Assigned"
                    document.getElementById('submission-status').textContent = 'Assigned';
                    document.getElementById('submission-status').className = 'text-success';
                } else {
                    alert('Failed to unsubmit: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error unsubmitting:', error);

                // Even if the server save fails, show the initial view
                document.getElementById('initial-actions').style.display = 'block';
                document.getElementById('turned-in-view').style.display = 'none';

                // Reset status to "Assigned"
                document.getElementById('submission-status').textContent = 'Assigned';
                document.getElementById('submission-status').className = 'text-success';
            });
        });
    }



    // Show comment input when link is clicked
    if (document.getElementById('add-comment-link')) {
        document.getElementById('add-comment-link').addEventListener('click', function(e) {
            e.preventDefault();
            document.getElementById('comment-input-container').style.display = 'block';
            document.getElementById('comment-input').focus();
        });
    }

    // Add comment functionality
    if (document.getElementById('send-comment-btn')) {
        document.getElementById('send-comment-btn').addEventListener('click', function() {
            const commentInput = document.getElementById('comment-input');
            const commentText = commentInput.value.trim();

            if (commentText !== '') {
                // Create a new comment object
                const now = new Date();
                const timeString = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

                // Get user profile picture
                <?php
                    $profilePic = '';
                    try {
                        $stmt = $pdo->prepare("SELECT profile_picture FROM users WHERE user_id = :userId");
                        $stmt->bindParam(':userId', $_SESSION['user_id']);
                        $stmt->execute();
                        if ($stmt->rowCount() > 0) {
                            $userData = $stmt->fetch();
                            if (!empty($userData['profile_picture'])) {
                                $profilePic = $userData['profile_picture'];
                            }
                        }
                    } catch (PDOException $e) {
                        // Silently fail and use default avatar
                    }
                ?>

                const comment = {
                    username: '<?php echo isset($_SESSION['first_name']) && isset($_SESSION['last_name']) ?
                        htmlspecialchars($_SESSION['first_name'] . ' ' . $_SESSION['last_name']) :
                        (isset($_SESSION['username']) ? htmlspecialchars($_SESSION['username']) : 'USER'); ?>',
                    content: commentText,
                    timestamp: timeString,
                    avatar: '<?php echo htmlspecialchars($profilePic); ?>',
                    date: now.toISOString()
                };

                // Save the comment to the server
                saveComment(comment);

                // Clear the input field and hide it
                commentInput.value = '';
                document.getElementById('comment-input-container').style.display = 'none';
            }
        });
    }

    // View Score button functionality
    if (document.querySelector('.view-score-btn')) {
        document.querySelector('.view-score-btn').addEventListener('click', function(e) {
            e.preventDefault();

            // Show the score modal
            $('#scoreModal').modal('show');
        });
    }

    // Function to save a comment
    function saveComment(comment) {
        // Create a form to submit the comment
        const formData = new FormData();
        formData.append('action', 'save_comment');
        formData.append('activity_id', '<?php echo $activityId; ?>');
        formData.append('comment', JSON.stringify(comment));

        // Send the comment to the server
        fetch('save_comment.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Add the comment to the UI
                addCommentToUI(comment, data.comment_id);
            } else {
                alert('Failed to save comment: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error saving comment:', error);

            // Even if the server save fails, show the comment in the UI
            // with a note that it's not saved
            addCommentToUI(comment, 'temp_' + Date.now());
        });
    }

    // Function to add a comment to the UI
    function addCommentToUI(comment, commentId) {
        const commentsContainer = document.getElementById('comments-container');

        // Check if there's a comment list already
        let commentList = commentsContainer.querySelector('.comment-list');
        if (!commentList) {
            commentList = document.createElement('div');
            commentList.className = 'comment-list';
            commentsContainer.appendChild(commentList);
        }

        // Create the comment element
        const commentElement = document.createElement('div');
        commentElement.className = 'comment-item mb-3';
        commentElement.dataset.commentId = commentId;

        // Create the comment HTML
        commentElement.innerHTML = `
            <div class="d-flex align-items-start">
                ${comment.avatar
                    ? `<img src="${comment.avatar}" class="rounded-circle mr-2" width="40" height="40" alt="User avatar" style="object-fit: cover;">`
                    : `<div class="rounded-circle mr-2 d-flex align-items-center justify-content-center bg-secondary text-white" style="width: 40px; height: 40px;">
                        ${comment.username.charAt(0).toUpperCase()}
                       </div>`
                }
                <div class="flex-grow-1">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong class="d-block">${comment.username}</strong>
                            <small class="text-muted">${comment.timestamp}</small>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-light rounded-circle" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="dropdown-menu dropdown-menu-right">
                                <a class="dropdown-item delete-comment" href="#" data-comment-id="${commentId}">Delete</a>
                            </div>
                        </div>
                    </div>
                    <p class="mb-0 mt-1">${comment.content}</p>
                </div>
            </div>
        `;

        // Add the comment to the list
        commentList.appendChild(commentElement);

        // Add event listener for the delete button
        const deleteButton = commentElement.querySelector('.delete-comment');
        deleteButton.addEventListener('click', handleDeleteComment);
    }

    // Function to format comment text in JavaScript
    function formatCommentTextJS(text) {
        if (!text) return '';

        // Escape HTML
        text = text.replace(/&/g, '&amp;')
                   .replace(/</g, '&lt;')
                   .replace(/>/g, '&gt;')
                   .replace(/"/g, '&quot;')
                   .replace(/'/g, '&#039;');

        // Convert newlines to <br>
        text = text.replace(/\n/g, '<br>');

        return text;
    }

    // Handle delete comment button clicks
    function handleDeleteComment(e) {
        e.preventDefault();
        const commentId = this.dataset.commentId;

        // Store the comment ID to be deleted
        document.getElementById('confirmDeleteComment').dataset.commentId = commentId;

        // Show the confirmation modal
        $('#deleteCommentModal').modal('show');
    }

    // Add event listeners to existing delete buttons
    document.querySelectorAll('.delete-comment').forEach(button => {
        button.addEventListener('click', handleDeleteComment);
    });

    // Handle confirm delete button click
    if (document.getElementById('confirmDeleteComment')) {
        document.getElementById('confirmDeleteComment').addEventListener('click', function() {
            const commentId = this.dataset.commentId;

            // Create a form to submit the delete request
            const formData = new FormData();
            formData.append('action', 'delete_comment');
            formData.append('activity_id', '<?php echo $activityId; ?>');
            formData.append('comment_id', commentId);

            // Send the delete request to the server
            fetch('save_comment.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remove the comment from the UI
                    const commentElement = document.querySelector(`.comment-item[data-comment-id="${commentId}"]`);
                    if (commentElement) {
                        commentElement.remove();
                    }
                } else {
                    alert('Failed to delete comment: ' + data.message);
                }

                // Hide the modal
                $('#deleteCommentModal').modal('hide');
            })
            .catch(error => {
                console.error('Error deleting comment:', error);
                alert('An error occurred while deleting the comment.');

                // Hide the modal
                $('#deleteCommentModal').modal('hide');
            });
        });
    }

    // Allow pressing Enter to send comment
    if (document.getElementById('comment-input')) {
        document.getElementById('comment-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('send-comment-btn').click();
            }
        });
    }

    // Initialize the UI based on submission status
    if (<?php echo $studentSubmission ? 'true' : 'false'; ?>) {
        // The PHP code now handles hiding the questionnaires completely,
        // but we'll still make sure any remaining elements are hidden

        // Update attachment status if available
        if (document.getElementById('attachment-status')) {
            <?php if ($studentSubmission && !empty($studentSubmission['file_path'])): ?>
            document.getElementById('attachment-status').textContent = '<?php echo basename($studentSubmission['file_path']); ?>';
            <?php elseif ($studentSubmission && !empty($studentSubmission['submission_link'])): ?>
            document.getElementById('attachment-status').textContent = 'Link: <?php echo $studentSubmission['submission_link']; ?>';
            <?php else: ?>
            document.getElementById('attachment-status').textContent = 'Marked as done';
            <?php endif; ?>
        }

        // Show the turned-in view if it exists
        if (document.getElementById('turned-in-view')) {
            document.getElementById('turned-in-view').style.display = 'block';
        }

        // Hide any initial actions if they exist
        if (document.getElementById('initial-actions')) {
            document.getElementById('initial-actions').style.display = 'none';
        }
    }
});
</script>

<style>
/* Enhanced option styling */
.option-container {
    transition: all 0.2s ease;
    cursor: pointer;
}

.option-container:hover {
    background-color: #f8f9fa;
}

.option-indicator .badge {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.option-text {
    flex: 1;
}

/* Card styling for questions */
.card-header.bg-light {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0,0,0,0.125);
}

.list-group-item.bg-light {
    background-color: rgba(40, 167, 69, 0.05);
}
</style>
