<?php
// Check if user is logged in
if (!isset($_SESSION['user_id']) && basename($_SERVER['PHP_SELF']) != 'login.php') {
    header("location: login.php");
    exit;
}

// Get current page for active menu highlighting
$current_page = basename($_SERVER['PHP_SELF'], '.php');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' : ''; ?><?php echo APP_NAME; ?></title>

    <?php
    // Check for theme preference
    $theme = isset($_COOKIE['theme']) ? $_COOKIE['theme'] : 'light';
    ?>

    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Google+Sans:wght@400;500;700&family=Roboto:wght@300;400;500;700&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="css/<?php echo $theme; ?>-theme.css">
    <link rel="stylesheet" href="css/text-editor.css">
    <style>
        /* Notification badge styles */
        .position-relative {
            position: relative;
        }
        .notification-badge {
            position: absolute;
            top: 0;
            right: 0;
            font-size: 0.6rem;
            padding: 0.2rem 0.4rem;
            transform: translate(50%, -50%);
        }

        /* User avatar styles */
        .user-avatar {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #4285f4;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            font-size: 14px;
            overflow: hidden;
        }

        /* Google Classroom style buttons */
        .course-header-bg {
            background-color: #4285f4 !important;
            color: white;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .course-actions .btn-group {
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            border-radius: 4px;
            overflow: hidden;
        }

        .course-actions .btn {
            padding: 10px 20px;
            font-weight: 500;
            border-radius: 4px;
            font-size: 15px;
            margin: 0 2px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.12);
            border-width: 1px;
        }

        .course-actions .btn-outline-primary {
            color: #4285f4;
            border-color: #4285f4;
        }

        .course-actions .btn-outline-primary:hover {
            background-color: #4285f4;
            color: white;
        }

        .course-actions .btn-outline-success {
            color: #0f9d58;
            border-color: #0f9d58;
        }

        .course-actions .btn-outline-success:hover {
            background-color: #0f9d58;
            color: white;
        }

        .course-actions .btn-outline-warning {
            color: #f4b400;
            border-color: #f4b400;
        }

        .course-actions .btn-outline-warning:hover {
            background-color: #f4b400;
            color: white;
        }

        .course-actions .btn-outline-danger {
            color: #db4437;
            border-color: #db4437;
        }

        .course-actions .btn-outline-danger:hover {
            background-color: #db4437;
            color: white;
        }

        /* Google Classroom style tabs */
        .nav-tabs {
            border-bottom: none;
        }

        .nav-tabs .nav-link {
            color: #5f6368;
            border: none;
            border-bottom: 3px solid transparent;
            font-weight: 500;
            padding: 12px 16px;
        }

        .nav-tabs .nav-link.active {
            color: #4285f4;
            background-color: transparent;
            border-bottom: 3px solid #4285f4;
        }

        .nav-tabs .nav-link:hover:not(.active) {
            border-bottom: 3px solid #dadce0;
        }

        /* Google Classroom style Your work and Private comments sections */
        .card.shadow-sm {
            border-radius: 8px;
            border: none;
            box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24) !important;
        }

        /* Your work section */
        #add-create-btn {
            border-radius: 4px;
            font-weight: 500;
            color: #1a73e8;
            border-color: #dadce0;
            background-color: #fff;
        }

        #add-create-btn:hover {
            background-color: #f8f9fa;
        }

        #mark-as-done-btn {
            border-radius: 4px;
            font-weight: 500;
            background-color: #4285f4;
            border-color: #4285f4;
        }

        #mark-as-done-btn:hover {
            background-color: #1a73e8;
            border-color: #1a73e8;
        }

        #unsubmit-btn {
            border-radius: 4px;
            font-weight: 500;
            color: #5f6368;
            border-color: #dadce0;
        }

        #unsubmit-btn:hover {
            background-color: #f8f9fa;
        }

        /* Private comments section */
        .comment-item {
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 10px;
        }

        .comment-item:last-child {
            border-bottom: none;
        }

        #add-comment-link {
            color: #1a73e8;
            text-decoration: none;
            font-weight: 500;
        }

        #add-comment-link:hover {
            text-decoration: underline;
        }

        .comment-input-container {
            border-color: #dadce0 !important;
            border-radius: 4px;
        }

        #comment-input {
            border-radius: 4px 0 0 4px;
        }

        #send-comment-btn {
            border-radius: 0 4px 4px 0;
            background-color: #fff;
        }

        #send-comment-btn:hover {
            background-color: #f8f9fa;
        }

        /* Dropdown menu styling */
        .dropdown-menu {
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            border: 1px solid #dadce0;
        }

        .dropdown-item {
            padding: 8px 16px;
            color: #3c4043;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
            color: #1a73e8;
        }

        .dropdown-divider {
            margin: 4px 0;
            border-top: 1px solid #e0e0e0;
        }

        /* Option container styling for multiple choice */
        .option-container {
            display: flex;
            align-items: center;
            padding: 10px 15px;
            margin-bottom: 8px;
            border: 1px solid #dadce0;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .option-container:hover {
            background-color: #f8f9fa;
        }

        .option-container.selected-option {
            background-color: #e8f0fe;
            border-color: #4285f4;
        }

        .option-circle {
            width: 20px;
            height: 20px;
            border: 2px solid #5f6368;
            border-radius: 50%;
            margin-right: 10px;
            position: relative;
            flex-shrink: 0;
        }

        .option-circle.selected:after {
            content: '';
            position: absolute;
            width: 10px;
            height: 10px;
            background-color: #4285f4;
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    </style>
    <!-- Dropdown Fix CSS -->
    <link rel="stylesheet" href="css/dropdown-fix.css">

    <?php if (isset($extra_css)): ?>
    <?php echo $extra_css; ?>
    <?php endif; ?>

    <!-- Dropdown Fix JavaScript -->
    <script src="js/dropdown-fix.js" defer></script>
</head>
<body class="<?php echo $theme === 'dark' ? 'dark-mode' : ''; ?>">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-md navbar-light fixed-top">
        <a class="navbar-brand" href="index.php">
            <i class="fas fa-graduation-cap"></i>
            <?php echo APP_NAME; ?>
        </a>
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarCollapse" aria-controls="navbarCollapse" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

        <?php if (isset($_SESSION['user_id'])): ?>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <ul class="navbar-nav ml-auto">
                <?php
                // Include notifications functions if not already included
                if (!function_exists('countUnreadNotifications')) {
                    require_once 'notifications.php';
                }

                // Get unread notification count
                $unreadNotificationCount = countUnreadNotifications($_SESSION['user_id']);
                ?>
                <li class="nav-item">
                    <a class="nav-link position-relative" href="notifications.php" title="Notifications">
                        <i class="fas fa-bell"></i>
                        <?php if ($unreadNotificationCount > 0): ?>
                        <span class="badge badge-danger notification-badge"><?php echo $unreadNotificationCount; ?></span>
                        <?php endif; ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" title="Help">
                        <i class="fas fa-question-circle"></i>
                    </a>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <div class="user-avatar">
                            <?php
                            // Get user profile picture if available
                            $userId = $_SESSION['user_id'];
                            $userProfilePic = null;

                            // We're accessing our own profile, so this should always work
                            // No need to use getUserById which has permission checks
                            try {
                                $stmt = $pdo->prepare("SELECT profile_picture FROM users WHERE user_id = :userId");
                                $stmt->bindParam(':userId', $userId);
                                $stmt->execute();

                                if ($stmt->rowCount() > 0) {
                                    $userData = $stmt->fetch();
                                    $userProfilePic = $userData['profile_picture'];
                                }
                            } catch (PDOException $e) {
                                // Silently fail and use default avatar
                            }

                            if (!empty($userProfilePic)):
                            ?>
                                <img src="<?php echo htmlspecialchars($userProfilePic); ?>" alt="Profile" class="rounded-circle" style="width: 30px; height: 30px; object-fit: cover;">
                            <?php else: ?>
                                <?php echo strtoupper(substr($_SESSION["first_name"], 0, 1)); ?>
                            <?php endif; ?>
                        </div>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right" aria-labelledby="navbarDropdown">
                        <a class="dropdown-item" href="profile.php"><i class="fas fa-id-card"></i> Profile</a>
                        <a class="dropdown-item" href="notifications.php"><i class="fas fa-bell"></i> Notifications
                            <?php if ($unreadNotificationCount > 0): ?>
                            <span class="badge badge-danger"><?php echo $unreadNotificationCount; ?></span>
                            <?php endif; ?>
                        </a>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </div>
                </li>
            </ul>
        </div>
        <?php endif; ?>
    </nav>

    <?php if (isset($_SESSION['user_id'])): ?>
    <!-- Sidebar -->
    <div class="sidebar">
        <!-- User Profile Section at Top of Sidebar -->
        <div class="text-center py-4">
            <?php
            // Get user profile picture if available
            $sidebarUserId = $_SESSION['user_id'];
            $sidebarUserProfilePic = null;
            $sidebarUserName = $_SESSION['first_name'] . ' ' . $_SESSION['last_name'];

            // We're accessing our own profile, so this should always work
            // No need to use getUserById which has permission checks
            try {
                $stmt = $pdo->prepare("SELECT profile_picture FROM users WHERE user_id = :userId");
                $stmt->bindParam(':userId', $sidebarUserId);
                $stmt->execute();

                if ($stmt->rowCount() > 0) {
                    $userData = $stmt->fetch();
                    $sidebarUserProfilePic = $userData['profile_picture'];
                }
            } catch (PDOException $e) {
                // Silently fail and use default avatar
            }

            if (!empty($sidebarUserProfilePic)):
            ?>
                <img src="<?php echo htmlspecialchars($sidebarUserProfilePic); ?>" alt="Profile" class="rounded-circle mb-2" style="width: 80px; height: 80px; object-fit: cover;">
            <?php else: ?>
                <div class="sidebar-avatar-placeholder rounded-circle d-flex align-items-center justify-content-center mx-auto mb-2" style="width: 80px; height: 80px; background-color: #e9ecef;">
                    <span style="font-size: 2rem; color: #6c757d;"><?php echo strtoupper(substr($_SESSION["first_name"], 0, 1)); ?></span>
                </div>
            <?php endif; ?>
            <h6 class="mb-0"><?php echo htmlspecialchars($sidebarUserName); ?></h6>
            <small class="text-muted"><?php echo ucfirst($_SESSION['role']); ?></small>
        </div>
        <hr>
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'index' ? 'active' : ''; ?>" href="index.php">
                    <i class="fas fa-home"></i> Home
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'calendar' ? 'active' : ''; ?>" href="calendar.php">
                    <i class="fas fa-calendar-alt"></i> Calendar
                </a>
            </li>
            <?php
            // Include notifications functions if not already included
            if (!function_exists('countUnreadNotifications')) {
                require_once 'notifications.php';
            }

            // Get unread notification count
            $sidebarUnreadCount = countUnreadNotifications($_SESSION['user_id']);
            ?>
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'notifications' ? 'active' : ''; ?>" href="notifications.php">
                    <i class="fas fa-bell"></i> Notifications
                    <?php if ($sidebarUnreadCount > 0): ?>
                    <span class="badge badge-danger"><?php echo $sidebarUnreadCount; ?></span>
                    <?php endif; ?>
                </a>
            </li>
            <?php if (isTeacher()): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'archive' ? 'active' : ''; ?>" href="archive.php">
                    <i class="fas fa-archive"></i> Archive
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'settings' ? 'active' : ''; ?>" href="user_settings.php">
                    <i class="fas fa-cog"></i> Settings
                </a>
            </li>
            <?php endif; ?>
            <?php if (isStudent()): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'archive' ? 'active' : ''; ?>" href="archive.php">
                    <i class="fas fa-archive"></i> Archive
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'settings' ? 'active' : ''; ?>" href="user_settings.php">
                    <i class="fas fa-cog"></i> Settings
                </a>
            </li>
            <?php endif; ?>
            <?php if (isAdmin()): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'users' ? 'active' : ''; ?>" href="users.php">
                    <i class="fas fa-users"></i> Users
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'settings' ? 'active' : ''; ?>" href="settings.php">
                    <i class="fas fa-cog"></i> Settings
                </a>
            </li>
            <?php endif; ?>
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'profile' ? 'active' : ''; ?>" href="profile.php">
                    <i class="fas fa-user"></i> Profile
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="logout.php">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </li>
        </ul>
    </div>
    <?php endif; ?>

    <!-- Main content -->
    <div class="<?php echo isset($_SESSION['user_id']) ? 'main-content' : ''; ?>">
        <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php
            echo $_SESSION['success'];
            unset($_SESSION['success']);
            ?>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php
            echo $_SESSION['error'];
            unset($_SESSION['error']);
            ?>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <?php endif; ?>
