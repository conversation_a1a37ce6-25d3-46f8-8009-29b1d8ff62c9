<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/course_functions.php';
require_once 'includes/dashboard_functions.php';

// Check if the user is logged in, if not redirect to landing page
if (!isLoggedIn()) {
    header("location: landing.php");
    exit;
}

// Clear any error messages that might be set from other pages
if (isset($_SESSION['error'])) {
    // Clear specific error messages or the unauthorized access message
    if ($_SESSION['error'] == "Question not found." ||
        $_SESSION['error'] == "Course not found." ||
        $_SESSION['error'] == "You are not authorized to access this user profile.") {
        unset($_SESSION['error']);
    }
}

// Get user's role
$role = $_SESSION['role'];

// Get courses based on user role (skip for admin)
if (!isAdmin()) {
    if (isTeacher()) {
        // Get teacher's courses, excluding archived ones
        $courses = getCoursesByTeacher($_SESSION['user_id'], true, false);
    } else {
        // Get student's enrolled courses, excluding archived ones
        $courses = getEnrolledCourses($_SESSION['user_id'], false);
    }

    // Check if courses is an error message
    if (is_string($courses)) {
        // Don't set any error message for "Course not found" on the main page
        if ($courses !== "Course not found.") {
            $error = $courses;
        }
        $courses = [];
    }

    // Filter out any archived courses that might have slipped through
    if (!empty($courses) && is_array($courses)) {
        foreach ($courses as $key => $course) {
            // Remove if either the course is archived or the enrollment is archived
            if ((isset($course['is_archived']) && $course['is_archived'] == 1) ||
                (isset($course['course_archived']) && $course['course_archived'] == 1)) {
                unset($courses[$key]);
            }
        }
        // Reindex array after filtering
        $courses = array_values($courses);
    }
} else {
    // Empty array for admin users since they don't see courses
    $courses = [];
    $availableCourses = [];
}

// Get dashboard data for admin
if (isAdmin()) {
    // Get dashboard counts
    $dashboardCounts = getDashboardCounts();
    if (is_string($dashboardCounts)) {
        $error = $dashboardCounts;
        $dashboardCounts = [
            'courses' => 0,
            'students' => 0,
            'teachers' => 0,
            'assignments' => 0
        ];
    }

    // Get enrollment trends
    $enrollmentTrends = getEnrollmentTrends();
    if (is_string($enrollmentTrends)) {
        $error = $enrollmentTrends;
        $enrollmentTrends = [
            'labels' => [],
            'enrollments' => [],
            'completions' => []
        ];
    }

    // Get user distribution
    $userDistribution = getUserDistribution();
    if (is_string($userDistribution)) {
        $error = $userDistribution;
        $userDistribution = [
            'labels' => [],
            'data' => []
        ];
    }

    // Get recent activity
    $recentActivity = getRecentActivity();
    if (is_string($recentActivity)) {
        $error = $recentActivity;
        $recentActivity = [];
    }

    // Get top performing courses
    $topCourses = getTopPerformingCourses();
    if (is_string($topCourses)) {
        $error = $topCourses;
        $topCourses = [];
    }

    // Add chart data to JavaScript
    $extra_js = '
    <script>
        // Override chart data with server data
        const enrollmentLabels = ' . json_encode($enrollmentTrends['labels']) . ';
        const enrollmentData = ' . json_encode($enrollmentTrends['enrollments']) . ';
        const completionData = ' . json_encode($enrollmentTrends['completions']) . ';

        const userLabels = ' . json_encode($userDistribution['labels']) . ';
        const userData = ' . json_encode($userDistribution['data']) . ';

        // Update chart data when DOM is loaded
        document.addEventListener("DOMContentLoaded", function() {
            if (window.enrollmentChart) {
                window.enrollmentChart.data.labels = enrollmentLabels;
                window.enrollmentChart.data.datasets[0].data = enrollmentData;
                window.enrollmentChart.data.datasets[1].data = completionData;
                window.enrollmentChart.update();
            }

            if (window.userDistributionChart) {
                window.userDistributionChart.data.labels = userLabels;
                window.userDistributionChart.data.datasets[0].data = userData;
                window.userDistributionChart.update();
            }
        });
    </script>
    ';
}

// Set page title
$page_title = "Dashboard";

// Include header
require_once 'includes/header.php';
?>

<?php if (isAdmin()): ?>
<!-- Admin Dashboard Section -->
<div class="dashboard-section mb-5">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>System Monitoring Dashboard</h1>
        <div>
            <button id="printDashboard" class="btn btn-outline-primary mr-2">
                <i class="fas fa-print"></i> Print Report
            </button>
            <div class="btn-group">
                <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="fas fa-calendar-alt"></i> This Month
                </button>
                <div class="dropdown-menu dropdown-menu-right">
                    <h6 class="dropdown-header">Time Range</h6>
                    <a class="dropdown-item" href="#" data-time-range="week">
                        <i class="fas fa-calendar-week mr-2"></i>This Week
                    </a>
                    <a class="dropdown-item active" href="#" data-time-range="month">
                        <i class="fas fa-calendar-alt mr-2"></i>This Month
                    </a>
                    <a class="dropdown-item" href="#" data-time-range="quarter">
                        <i class="fas fa-calendar mr-2"></i>This Quarter
                    </a>
                    <a class="dropdown-item" href="#" data-time-range="year">
                        <i class="fas fa-calendar-check mr-2"></i>This Year
                    </a>
                    <div class="dropdown-divider"></div>
                    <a class="dropdown-item" href="#" data-time-range="custom">
                        <i class="fas fa-calendar-plus mr-2"></i>Custom Range
                    </a>
                </div>
            </div>
            <div class="btn-group ml-2">
                <button type="button" class="btn btn-outline-info dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" id="semesterFilterButton">
                    <i class="fas fa-graduation-cap"></i> All Semesters
                </button>
                <div class="dropdown-menu dropdown-menu-right">
                    <h6 class="dropdown-header">Filter by Semester</h6>
                    <a class="dropdown-item active" href="#" data-semester-filter="all">
                        <i class="fas fa-list mr-2"></i>All Semesters
                    </a>
                    <div class="dropdown-divider"></div>
                    <a class="dropdown-item" href="#" data-semester-filter="first">
                        <i class="fas fa-calendar-alt mr-2"></i>First Semester
                    </a>
                    <a class="dropdown-item" href="#" data-semester-filter="second">
                        <i class="fas fa-calendar-alt mr-2"></i>Second Semester
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Admin Role Description -->
    <div class="alert alert-info mb-4">
        <div class="d-flex align-items-center">
            <i class="fas fa-info-circle mr-3 fa-2x"></i>
            <div>
                <h5 class="alert-heading mb-1">Administrator Role</h5>
                <p class="mb-0">As an administrator, your role is focused on system monitoring and control. Use this dashboard to track system usage, manage users, and monitor overall platform performance. Use the semester filter to view data specific to First or Second semester courses.</p>
            </div>
        </div>
    </div>

    <style>
        /* Enhanced dropdown styling */
        .btn-group .dropdown-toggle {
            border-radius: 6px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .btn-group .dropdown-toggle::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-group .dropdown-toggle:hover::before {
            left: 100%;
        }

        /* Time range filter styling */
        .btn-group .btn-outline-secondary {
            border-color: #6c757d;
            color: #6c757d;
        }

        .btn-group .btn-outline-secondary:hover {
            background-color: #6c757d;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
        }

        /* Semester filter styling */
        #semesterFilterButton {
            border-color: #17a2b8;
            color: #17a2b8;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        #semesterFilterButton:hover {
            background-color: #17a2b8;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(23, 162, 184, 0.4);
        }

        #semesterFilterButton:focus {
            box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
        }

        /* Enhanced dropdown menu styling */
        .dropdown-menu {
            border: none;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            border-radius: 8px;
            padding: 8px 0;
            margin-top: 8px;
            animation: dropdownFadeIn 0.3s ease-out;
            transform-origin: top;
        }

        @keyframes dropdownFadeIn {
            0% {
                opacity: 0;
                transform: translateY(-10px) scale(0.95);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .dropdown-header {
            color: #17a2b8 !important;
            font-weight: 600;
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            padding: 8px 16px 4px;
            margin-bottom: 4px;
            border-bottom: 1px solid rgba(23, 162, 184, 0.1);
        }

        .dropdown-divider {
            margin: 4px 0;
            border-color: rgba(0, 0, 0, 0.08);
        }

        /* Enhanced dropdown items */
        .dropdown-item {
            padding: 10px 16px;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 4px;
            margin: 2px 8px;
            position: relative;
            overflow: hidden;
        }

        .dropdown-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.4s;
        }

        .dropdown-item:hover::before {
            left: 100%;
        }

        .dropdown-item[data-semester-filter] {
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .dropdown-item[data-semester-filter]:hover {
            background-color: rgba(23, 162, 184, 0.1);
            color: #17a2b8;
            transform: translateX(8px);
            box-shadow: 0 2px 8px rgba(23, 162, 184, 0.2);
        }

        .dropdown-item[data-semester-filter].active {
            background-color: #17a2b8;
            color: white;
            box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
        }

        .dropdown-item[data-semester-filter].active:hover {
            background-color: #138496;
            transform: translateX(4px);
        }

        .dropdown-item[data-time-range]:hover {
            background-color: rgba(108, 117, 125, 0.1);
            color: #495057;
            transform: translateX(8px);
            box-shadow: 0 2px 8px rgba(108, 117, 125, 0.2);
        }

        .dropdown-item[data-time-range].active {
            background-color: #6c757d;
            color: white;
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
        }

        .dropdown-item i {
            transition: transform 0.2s ease;
        }

        .dropdown-item:hover i {
            transform: scale(1.1);
        }

        /* Loading state for dashboard */
        .dashboard-loading {
            position: relative;
            transition: opacity 0.3s ease;
        }

        .dashboard-loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
            backdrop-filter: blur(2px);
        }

        /* Smooth transitions for dashboard content */
        #dashboardContent {
            transition: opacity 0.3s ease, transform 0.3s ease;
        }

        #dashboardContent.updating {
            opacity: 0.7;
            transform: translateY(10px);
        }

        /* Enhanced button hover effects */
        .btn {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .btn:hover {
            transform: translateY(-1px);
        }

        .btn:active {
            transform: translateY(0);
        }

        /* Notification styles */
        .notification-toast {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            animation: slideInRight 0.3s ease-out;
        }

        @keyframes slideInRight {
            0% {
                opacity: 0;
                transform: translateX(100%);
            }
            100% {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Responsive dropdown adjustments */
        @media (max-width: 768px) {
            .dropdown-menu {
                margin-top: 4px;
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
            }

            .dropdown-item {
                padding: 12px 16px;
            }

            .dropdown-item:hover {
                transform: none;
            }
        }
    </style>

    <div class="row" id="dashboardContent">
        <!-- System Stats Cards -->
        <div class="col-md-3 mb-4">
            <div class="card dashboard-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Active Users</h6>
                            <h3 class="mb-0"><?php echo $dashboardCounts['students'] + $dashboardCounts['teachers']; ?></h3>
                        </div>
                        <div class="dashboard-icon bg-primary-light">
                            <i class="fas fa-users text-primary"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <span class="text-success"><i class="fas fa-arrow-up"></i> 10.5%</span>
                        <span class="text-muted ml-2">Since last month</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="card dashboard-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Total Students</h6>
                            <h3 class="mb-0"><?php echo $dashboardCounts['students']; ?></h3>
                        </div>
                        <div class="dashboard-icon bg-success-light">
                            <i class="fas fa-user-graduate text-success"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <span class="text-success"><i class="fas fa-arrow-up"></i> 12.5%</span>
                        <span class="text-muted ml-2">Since last month</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="card dashboard-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Total Teachers</h6>
                            <h3 class="mb-0"><?php echo $dashboardCounts['teachers']; ?></h3>
                        </div>
                        <div class="dashboard-icon bg-info-light">
                            <i class="fas fa-chalkboard-teacher text-info"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <span class="text-success"><i class="fas fa-arrow-up"></i> 5.0%</span>
                        <span class="text-muted ml-2">Since last month</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="card dashboard-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Total Courses</h6>
                            <h3 class="mb-0"><?php echo $dashboardCounts['courses']; ?></h3>
                        </div>
                        <div class="dashboard-icon bg-warning-light">
                            <i class="fas fa-book text-warning"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <span class="text-success"><i class="fas fa-arrow-up"></i> 8.3%</span>
                        <span class="text-muted ml-2">Since last month</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="users.php" class="btn btn-light btn-block py-3">
                                <i class="fas fa-user-plus text-primary mb-2 fa-2x"></i>
                                <div>Manage Users</div>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="manage_teachers.php" class="btn btn-light btn-block py-3">
                                <i class="fas fa-chalkboard-teacher text-info mb-2 fa-2x"></i>
                                <div>Manage Teachers</div>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="manage_students.php" class="btn btn-light btn-block py-3">
                                <i class="fas fa-user-graduate text-success mb-2 fa-2x"></i>
                                <div>Manage Students</div>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="admin_settings.php" class="btn btn-light btn-block py-3">
                                <i class="fas fa-cog text-secondary mb-2 fa-2x"></i>
                                <div>System Settings</div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts -->
        <div class="col-md-8 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">System Usage Trends</h5>
                    <div class="btn-group btn-group-sm chart-tabs">
                        <button type="button" class="btn btn-outline-secondary active" data-chart-type="enrollments">Enrollments</button>
                        <button type="button" class="btn btn-outline-secondary" data-chart-type="completions">Completions</button>
                        <button type="button" class="btn btn-outline-secondary" data-chart-type="activity">Activity</button>
                    </div>
                </div>
                <div class="card-body">
                    <canvas id="enrollmentChart" height="300"></canvas>
                </div>
                <div class="card-footer bg-white">
                    <div class="d-flex justify-content-between text-muted">
                        <small><i class="fas fa-info-circle"></i> This chart shows enrollment and completion trends over the last 6 months</small>
                        <small><a href="#" class="text-muted">View Detailed Report</a></small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">User Distribution</h5>
                </div>
                <div class="card-body">
                    <canvas id="userDistributionChart" height="300"></canvas>
                </div>
                <div class="card-footer bg-white">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="font-weight-bold"><?php echo $dashboardCounts['students']; ?></div>
                            <small class="text-muted">Students</small>
                        </div>
                        <div class="col-4">
                            <div class="font-weight-bold"><?php echo $dashboardCounts['teachers']; ?></div>
                            <small class="text-muted">Teachers</small>
                        </div>
                        <div class="col-4">
                            <div class="font-weight-bold">1</div>
                            <small class="text-muted">Admins</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Recent System Activity</h5>
                    <a href="#" class="text-muted">View All</a>
                </div>
                <div class="list-group list-group-flush">
                    <?php if (count($recentActivity) > 0): ?>
                        <?php foreach ($recentActivity as $activity): ?>
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1"><?php echo htmlspecialchars($activity['title']); ?></h6>
                                <small class="text-muted"><?php echo htmlspecialchars($activity['time']); ?></small>
                            </div>
                            <small class="text-muted"><?php echo htmlspecialchars($activity['description']); ?></small>
                        </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="list-group-item text-center py-4">
                            <p class="text-muted mb-0">No recent activity</p>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="card-footer bg-white">
                    <small class="text-muted"><i class="fas fa-info-circle"></i> System activity is updated in real-time</small>
                </div>
            </div>
        </div>

        <!-- Top Performing Courses -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Top Active Courses</h5>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="courseFilterDropdown" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            Filter
                        </button>
                        <div class="dropdown-menu dropdown-menu-right" aria-labelledby="courseFilterDropdown">
                            <a class="dropdown-item active" href="#" data-filter="enrollment">By Enrollment</a>
                            <a class="dropdown-item" href="#" data-filter="completion">By Completion Rate</a>
                            <a class="dropdown-item" href="#" data-filter="activity">By Activity</a>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Course Name</th>
                                <th>Students</th>
                                <th>Completion</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (count($topCourses) > 0): ?>
                                <?php foreach ($topCourses as $course): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($course['title']); ?></td>
                                    <td><?php echo $course['students']; ?></td>
                                    <td>
                                        <div class="progress" style="height: 6px;">
                                            <?php
                                            $colorClass = 'bg-primary';
                                            if ($course['completion'] >= 80) {
                                                $colorClass = 'bg-success';
                                            } elseif ($course['completion'] >= 60) {
                                                $colorClass = 'bg-info';
                                            } elseif ($course['completion'] >= 40) {
                                                $colorClass = 'bg-warning';
                                            } else {
                                                $colorClass = 'bg-danger';
                                            }
                                            ?>
                                            <div class="progress-bar <?php echo $colorClass; ?>" role="progressbar"
                                                style="width: <?php echo $course['completion']; ?>%;"
                                                aria-valuenow="<?php echo $course['completion']; ?>"
                                                aria-valuemin="0"
                                                aria-valuemax="100"></div>
                                        </div>
                                        <small class="text-muted"><?php echo $course['completion']; ?>%</small>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="3" class="text-center py-4">No course data available</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
                <div class="card-footer bg-white">
                    <small class="text-muted"><i class="fas fa-info-circle"></i> Course statistics are updated daily</small>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php if (!isAdmin()): ?>

<!-- Custom CSS for course cards and buttons -->
<style>
    /* Course card styles */
    .course-card {
        border-radius: 10px;
        overflow: hidden;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
        background-color: #fff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
    }

    .course-card-header {
        padding: 15px;
        background-color: var(--primary-color);
        color: white;
    }

    .course-card-title {
        font-size: 1.25rem;
        margin-bottom: 5px;
    }

    .course-card-subtitle {
        font-size: 0.9rem;
        opacity: 0.8;
        margin-bottom: 0;
    }

    .course-card-body {
        padding: 15px;
        flex-grow: 1;
    }

    .course-info {
        margin-top: 15px;
        font-size: 0.9rem;
        color: #666;
    }

    .course-info i {
        width: 20px;
        text-align: center;
        margin-right: 5px;
    }

    .course-card-footer {
        padding: 10px;
        background-color: #f8f9fa;
        border-top: 1px solid #e9ecef;
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
        justify-content: flex-end;
    }

    /* Cute button styles */
    .btn-cute {
        border-radius: 6px;
        padding: 6px 8px;
        font-weight: 500;
        font-size: 11px;
        text-align: center;
        transition: all 0.3s ease;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 55px;
        height: 50px;
        border: none;
        margin: 0 3px;
        text-decoration: none;
    }

    .btn-cute i {
        font-size: 16px;
        margin-bottom: 3px;
    }

    .btn-cute-primary {
        background-color: #4285F4;
        color: white;
    }

    .btn-cute-primary:hover {
        background-color: #3367d6;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    .btn-cute-success {
        background-color: #34A853;
        color: white;
    }

    .btn-cute-success:hover {
        background-color: #2d8d46;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    .btn-cute-secondary {
        background-color: #f1f3f4;
        color: #5f6368;
    }

    .btn-cute-secondary:hover {
        background-color: #e8eaed;
        color: #202124;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    .btn-cute-warning {
        background-color: #FBBC05;
        color: white;
    }

    .btn-cute-warning:hover {
        background-color: #f9a825;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    .btn-cute-outline-warning {
        background-color: white;
        color: #FBBC05;
        border: 1px solid #FBBC05;
    }

    .btn-cute-outline-warning:hover {
        background-color: #fff8e1;
        color: #f9a825;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    .btn-cute .badge {
        position: absolute;
        top: 2px;
        right: 2px;
        border-radius: 50%;
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 9px;
        font-weight: bold;
        padding: 0;
    }

    .course-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
    }
</style>

<!-- Courses Section -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Courses</h1>
    <div>
        <?php if (isStudent()): ?>
        <a href="course_join.php" class="btn btn-outline-primary mr-2">
            <i class="fas fa-sign-in-alt"></i> Join Course
        </a>
        <?php endif; ?>
        <?php if (isTeacher()): ?>
        <a href="course_add.php" class="btn btn-primary">
            <i class="fas fa-plus"></i> Create Course
        </a>
        <?php endif; ?>
    </div>
</div>

<?php if (isset($_SESSION['success'])): ?>
<div class="alert alert-success">
    <?php
    echo $_SESSION['success'];
    unset($_SESSION['success']);
    ?>
</div>
<?php endif; ?>

<?php if (isset($_SESSION['error'])): ?>
<div class="alert alert-danger">
    <?php
    echo $_SESSION['error'];
    unset($_SESSION['error']);
    ?>
</div>
<?php endif; ?>

<?php if (isset($error) && $error !== "Course not found."): ?>
<div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<?php
// We've removed the archived courses section from the home page
// Users can access archived courses through the Archive link in the sidebar
?>

<?php if (count($courses) > 0): ?>
    <h2 class="mb-3">Your Courses</h2>
    <div class="course-grid">
        <?php foreach ($courses as $course): ?>
        <div class="card course-card">
            <div class="course-card-header">
                <h2 class="course-card-title"><?php echo htmlspecialchars($course['title']); ?></h2>
                <p class="course-card-subtitle">
                    <?php if (isset($course['creator_name'])): ?>
                    instructor
                    <?php endif; ?>
                </p>
            </div>
            <div class="course-card-body">
                <p><?php echo htmlspecialchars(substr($course['description'], 0, 100) . (strlen($course['description']) > 100 ? '...' : '')); ?></p>

                <div class="course-info">
                    <div>
                        <i class="far fa-calendar-alt"></i> Created: <?php echo date('F j, Y', strtotime($course['created_at'])); ?>
                    </div>

                    <?php if (isset($course['semester']) && !empty($course['semester'])): ?>
                    <div class="mt-1">
                        <i class="fas fa-university"></i> <?php echo $course['semester'] === 'first' ? 'First Semester' : 'Second Semester'; ?>
                    </div>
                    <?php endif; ?>
                </div>

                <div class="mt-3">
                    <?php if (isset($course['created_by']) && $course['created_by'] == $_SESSION['user_id']): ?>
                    <span class="badge badge-primary">Creator</span>
                    <?php elseif (isTeacher() && isInstructor($_SESSION['user_id'], $course['course_id'])): ?>
                    <span class="badge badge-success">Instructor</span>
                    <?php endif; ?>

                    <?php if (isset($course['is_active'])): ?>
                        <?php if ($course['is_active']): ?>
                            <span class="badge badge-success"><i class="fas fa-check-circle mr-1"></i> Active</span>
                        <?php else: ?>
                            <span class="badge badge-danger"><i class="fas fa-ban mr-1"></i> Inactive</span>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
            <div class="course-card-footer">
                <a href="course_view_full.php?id=<?php echo $course['course_id']; ?>" class="btn-cute btn-cute-primary">
                    <i class="fas fa-folder-open"></i>
                    Open
                </a>

                <?php if (isStudent() && isset($course['completion_status']) && $course['completion_status'] == 'completed'): ?>
                <span class="btn-cute btn-cute-secondary disabled">
                    <i class="fas fa-check-circle"></i>
                    Completed
                </span>
                <?php elseif (isTeacher() && (isset($course['created_by']) && $course['created_by'] == $_SESSION['user_id'] || isInstructor($_SESSION['user_id'], $course['course_id']))): ?>
                <a href="course_action.php?action=complete&id=<?php echo $course['course_id']; ?>"
                   class="btn-cute btn-cute-success"
                   onclick="return confirm('Are you sure you want to mark this course as completed for ALL enrolled students? This action cannot be undone.');">
                    <i class="fas fa-check-circle"></i>
                    Complete
                </a>
                <?php endif; ?>

                <?php if ((isTeacher() && isset($course['created_by']) && $course['created_by'] == $_SESSION['user_id']) ||
                         (isTeacher() && isInstructor($_SESSION['user_id'], $course['course_id']))): ?>
                <a href="course_edit.php?id=<?php echo $course['course_id']; ?>" class="btn-cute btn-cute-secondary">
                    <i class="fas fa-edit"></i>
                    Edit
                </a>
                <?php endif; ?>

                <?php
                // Get count of pending enrollment requests for all users
                if (isset($course['course_id'])):
                    $requestCount = countPendingEnrollmentRequests($course['course_id']);
                endif;
                ?>

                <?php if (isTeacher() && (isset($course['created_by']) && $course['created_by'] == $_SESSION['user_id'] || isInstructor($_SESSION['user_id'], $course['course_id']))): ?>
                    <?php if (isset($requestCount) && $requestCount > 0): ?>
                    <a href="course_enrollment_requests.php?id=<?php echo $course['course_id']; ?>"
                       class="btn-cute btn-cute-warning position-relative">
                        <i class="fas fa-user-slash"></i>
                        Requests
                        <span class="badge badge-light"><?php echo $requestCount; ?></span>
                    </a>
                    <?php else: ?>
                    <a href="course_enrollment_requests.php?id=<?php echo $course['course_id']; ?>"
                       class="btn-cute btn-cute-outline-warning position-relative">
                        <i class="fas fa-user-slash"></i>
                        Requests
                        <span class="badge badge-secondary">0</span>
                    </a>
                    <?php endif; ?>
                <?php endif; ?>


            </div>
        </div>
        <?php endforeach; ?>
    </div>
<?php else: ?>
<div class="empty-state">
    <div class="empty-state-icon">
        <i class="fas fa-book-open"></i>
    </div>
    <h3>No courses yet</h3>
    <p class="empty-state-text">Courses you're enrolled in or teaching will appear here</p>
    <?php if (isStudent()): ?>
    <a href="courses_browse.php" class="btn btn-primary">Browse Courses</a>
    <?php elseif (isTeacher()): ?>
    <a href="course_add.php" class="btn btn-primary">Create Your First Course</a>
    <?php endif; ?>
</div>
<?php endif; ?>

<!-- Archived Courses Section has been removed from the home page -->
<!-- Users can access archived courses through the Archive link in the sidebar -->

<?php endif; ?> <!-- End of if (!isAdmin()) -->

<?php
// Include footer
require_once 'includes/footer.php';
?>
