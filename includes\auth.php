<?php
/**
 * Authentication Functions
 *
 * This file contains functions related to user authentication.
 */

require_once 'config.php';

/**
 * Function to register a new user
 * Only admin can create new users
 *
 * @param string $username The username
 * @param string $password The password
 * @param string $email The email address
 * @param string $firstName The first name
 * @param string $lastName The last name
 * @param int $roleId The role ID
 * @param string $gender The gender (optional)
 * @param string $birthday The birthday (optional)
 * @param string $phoneNumber The phone number (optional)
 * @return bool|string True if registration successful, error message otherwise
 */
function registerUser($username, $password, $email, $firstName, $lastName, $roleId, $gender = null, $birthday = null, $phoneNumber = null) {
    global $pdo;

    // Only admin can register new users
    if (!isAdmin()) {
        return "Only administrators can create new user accounts.";
    }

    try {
        // Check if username already exists
        $stmt = $pdo->prepare("SELECT user_id FROM users WHERE username = :username");
        $stmt->bindParam(':username', $username);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            return "Username already exists.";
        }

        // Check if email already exists
        $stmt = $pdo->prepare("SELECT user_id FROM users WHERE email = :email");
        $stmt->bindParam(':email', $email);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            return "Email already exists.";
        }

        // Hash the password
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

        // Build the SQL query based on which optional fields are provided
        $sql = "INSERT INTO users (username, password, email, first_name, last_name, role_id";
        $params = [
            ':username' => $username,
            ':password' => $hashedPassword,
            ':email' => $email,
            ':firstName' => $firstName,
            ':lastName' => $lastName,
            ':roleId' => $roleId
        ];

        // Add optional fields if they are provided
        if ($gender !== null) {
            $sql .= ", gender";
            $params[':gender'] = $gender;
        }

        if ($birthday !== null) {
            $sql .= ", birthday";
            $params[':birthday'] = $birthday;
        }

        if ($phoneNumber !== null) {
            $sql .= ", phone_number";
            $params[':phoneNumber'] = $phoneNumber;
        }

        // Complete the SQL query
        $sql .= ") VALUES (:username, :password, :email, :firstName, :lastName, :roleId";

        if ($gender !== null) {
            $sql .= ", :gender";
        }

        if ($birthday !== null) {
            $sql .= ", :birthday";
        }

        if ($phoneNumber !== null) {
            $sql .= ", :phoneNumber";
        }

        $sql .= ")";

        // Prepare and execute the statement
        $stmt = $pdo->prepare($sql);

        // Bind all parameters
        foreach ($params as $param => $value) {
            $stmt->bindValue($param, $value);
        }

        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        return "Registration failed: " . $e->getMessage();
    }
}

/**
 * Function to authenticate a user
 *
 * @param string $username The username
 * @param string $password The password
 * @return bool|string True if login successful, error message otherwise
 */
function loginUser($username, $password) {
    global $pdo;

    try {
        // Get user data
        $stmt = $pdo->prepare("
            SELECT u.user_id, u.username, u.password, u.email, u.first_name, u.last_name, u.is_active, u.role_id, r.role_name
            FROM users u
            LEFT JOIN roles r ON u.role_id = r.role_id
            WHERE u.username = :username
        ");
        $stmt->bindParam(':username', $username);
        $stmt->execute();

        if ($stmt->rowCount() == 1) {
            $user = $stmt->fetch();

            // Check if account is active
            if (!$user['is_active']) {
                return "Your account has been deactivated. Please contact the administrator.";
            }

            // Verify password
            if (password_verify($password, $user['password'])) {
                // Password is correct, start a new session
                session_regenerate_id(true);

                // Store data in session variables
                $_SESSION['user_id'] = $user['user_id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['email'] = $user['email'];
                $_SESSION['first_name'] = $user['first_name'];
                $_SESSION['last_name'] = $user['last_name'];

                // Handle role - if role_name is missing, use a default based on role_id
                if (!empty($user['role_name'])) {
                    $_SESSION['role'] = $user['role_name'];
                } else {
                    // Determine role name based on role_id
                    switch ($user['role_id']) {
                        case 1:
                            $_SESSION['role'] = 'admin';
                            break;
                        case 2:
                            $_SESSION['role'] = 'teacher';
                            break;
                        default:
                            $_SESSION['role'] = 'student';
                            break;
                    }
                }

                return true;
            } else {
                return "Invalid password.";
            }
        } else {
            return "User not found.";
        }
    } catch (PDOException $e) {
        return "Login failed: " . $e->getMessage();
    }
}

/**
 * Function to handle profile picture upload
 *
 * @param array $file The uploaded file ($_FILES['profile_picture'])
 * @param int $userId The user ID
 * @return string|bool The profile picture path if successful, false if failed
 */
function uploadProfilePicture($file, $userId) {
    // Check if file was uploaded without errors
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return false;
    }

    // Validate file type
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
    if (!in_array($file['type'], $allowedTypes)) {
        return false;
    }

    // Validate file size (max 2MB)
    if ($file['size'] > 2 * 1024 * 1024) {
        return false;
    }

    // Create upload directory if it doesn't exist
    $uploadDir = 'uploads/profile_pics/';
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }

    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = 'profile_' . $userId . '_' . time() . '.' . $extension;
    $targetPath = $uploadDir . $filename;

    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $targetPath)) {
        return $targetPath;
    }

    return false;
}

/**
 * Function to update a user's profile
 * Admin can update any user, users can only update their own profile
 *
 * @param int $userId The user ID
 * @param string $email The email address
 * @param string $firstName The first name
 * @param string $lastName The last name
 * @param string|null $gender The gender (male, female, other)
 * @param string|null $birthday The birthday (YYYY-MM-DD)
 * @param string|null $phoneNumber The phone number
 * @param array|null $profilePicture The profile picture file ($_FILES['profile_picture'])
 * @param int|null $roleId The role ID (admin only)
 * @param bool|null $isActive Active status (admin only)
 * @return bool|string True if update successful, error message otherwise
 */
function updateUser($userId, $email, $firstName, $lastName, $gender = null, $birthday = null, $phoneNumber = null, $profilePicture = null, $roleId = null, $isActive = null) {
    global $pdo;

    // Check if user is authorized to update this profile
    if (!isAdmin() && $_SESSION['user_id'] != $userId) {
        return "You are not authorized to update this user profile.";
    }

    try {
        // Start building the query
        $query = "UPDATE users SET email = :email, first_name = :firstName, last_name = :lastName";
        $params = [
            ':email' => $email,
            ':firstName' => $firstName,
            ':lastName' => $lastName,
            ':userId' => $userId
        ];

        // Add optional fields if they are provided
        if ($gender !== null) {
            $query .= ", gender = :gender";
            $params[':gender'] = $gender;
        }

        if ($birthday !== null) {
            $query .= ", birthday = :birthday";
            $params[':birthday'] = $birthday;
        }

        if ($phoneNumber !== null) {
            $query .= ", phone_number = :phoneNumber";
            $params[':phoneNumber'] = $phoneNumber;
        }

        // Handle profile picture upload if provided
        if ($profilePicture !== null && $profilePicture['size'] > 0) {
            $profilePicturePath = uploadProfilePicture($profilePicture, $userId);
            if ($profilePicturePath) {
                $query .= ", profile_picture = :profilePicture";
                $params[':profilePicture'] = $profilePicturePath;
            }
        }

        // Only admin can update role and active status
        if (isAdmin()) {
            if ($roleId !== null) {
                $query .= ", role_id = :roleId";
                $params[':roleId'] = $roleId;
            }

            if ($isActive !== null) {
                $query .= ", is_active = :isActive";
                $params[':isActive'] = $isActive;
            }
        }

        $query .= " WHERE user_id = :userId";

        // Execute the query
        $stmt = $pdo->prepare($query);
        $stmt->execute($params);

        return true;
    } catch (PDOException $e) {
        return "Update failed: " . $e->getMessage();
    }
}

/**
 * Function to change a user's password
 * Admin can change any user's password, users can only change their own password
 *
 * @param int $userId The user ID
 * @param string $currentPassword The current password (not required for admin)
 * @param string $newPassword The new password
 * @return bool|string True if change successful, error message otherwise
 */
function changePassword($userId, $currentPassword, $newPassword) {
    global $pdo;

    // Check if user is authorized to change this password
    if (!isAdmin() && $_SESSION['user_id'] != $userId) {
        return "You are not authorized to change this user's password.";
    }

    try {
        // Get the current password hash
        $stmt = $pdo->prepare("SELECT password FROM users WHERE user_id = :userId");
        $stmt->bindParam(':userId', $userId);
        $stmt->execute();

        if ($stmt->rowCount() == 1) {
            $user = $stmt->fetch();

            // If not admin, verify current password
            if (!isAdmin()) {
                if (!password_verify($currentPassword, $user['password'])) {
                    return "Current password is incorrect.";
                }
            }

            // Hash the new password
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);

            // Update the password
            $stmt = $pdo->prepare("UPDATE users SET password = :password WHERE user_id = :userId");
            $stmt->bindParam(':password', $hashedPassword);
            $stmt->bindParam(':userId', $userId);
            $stmt->execute();

            return true;
        } else {
            return "User not found.";
        }
    } catch (PDOException $e) {
        return "Password change failed: " . $e->getMessage();
    }
}

/**
 * Function to delete a user
 * Only admin can delete users
 *
 * @param int $userId The user ID
 * @return bool|string True if deletion successful, error message otherwise
 */
function deleteUser($userId) {
    global $pdo;

    // Only admin can delete users
    if (!isAdmin()) {
        return "Only administrators can delete user accounts.";
    }

    try {
        // Delete the user
        $stmt = $pdo->prepare("DELETE FROM users WHERE user_id = :userId");
        $stmt->bindParam(':userId', $userId);
        $stmt->execute();

        if ($stmt->rowCount() == 1) {
            return true;
        } else {
            return "User not found.";
        }
    } catch (PDOException $e) {
        return "Deletion failed: " . $e->getMessage();
    }
}

/**
 * Function to log out a user
 *
 * @return void
 */
function logoutUser() {
    // Unset all session variables
    $_SESSION = [];

    // Destroy the session
    session_destroy();

    // Redirect to login page
    redirect('login.php');
}
?>
