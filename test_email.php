<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/simple_mailer.php';

// Check if user is logged in and is an admin
if (!isLoggedIn() || $_SESSION['role_id'] != 1) {
    header("location: login.php");
    exit;
}

// Initialize variables
$success_msg = "";
$error_msg = "";
$to = "";

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Get the form data
    $to = trim($_POST["to"]);
    
    // Validate the form data
    if (empty($to)) {
        $error_msg = "Recipient email is required.";
    } elseif (!filter_var($to, FILTER_VALIDATE_EMAIL)) {
        $error_msg = "Recipient email must be a valid email address.";
    } else {
        // Send a test email
        $subject = APP_NAME . " - Test Email";
        $message = "Hello,\n\n";
        $message .= "This is a test email from " . APP_NAME . ".\n\n";
        $message .= "If you received this email, your email configuration is working correctly.\n\n";
        $message .= "Sent at: " . date('Y-m-d H:i:s') . "\n\n";
        $message .= "Regards,\n" . APP_NAME . " Team";
        
        if (SimpleMailer::sendEmail($to, $subject, $message)) {
            $success_msg = "Test email sent successfully to " . $to . ". Please check your inbox.";
        } else {
            $error_msg = "Failed to send test email. Please check your email configuration.";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Email - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Google+Sans:wght@400;500;700&family=Roboto:wght@300;400;500;700&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="css/light-theme.css">
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            padding: 20px;
        }
        .card {
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            padding: 15px 20px;
        }
        .card-body {
            padding: 20px;
        }
        .form-group label {
            font-weight: 500;
        }
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        .btn-primary:hover {
            background-color: var(--primary-hover);
            border-color: var(--primary-hover);
        }
        .back-link {
            margin-bottom: 20px;
            display: inline-block;
        }
        .email-log {
            max-height: 300px;
            overflow-y: auto;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="email_settings.php" class="back-link"><i class="fas fa-arrow-left"></i> Back to Email Settings</a>
        
        <h1><i class="fas fa-paper-plane"></i> Test Email</h1>
        <p class="text-muted">Send a test email to verify your email configuration.</p>
        
        <?php if (!empty($error_msg)): ?>
            <div class="alert alert-danger"><?php echo $error_msg; ?></div>
        <?php endif; ?>
        
        <?php if (!empty($success_msg)): ?>
            <div class="alert alert-success"><?php echo $success_msg; ?></div>
        <?php endif; ?>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-envelope"></i> Send Test Email</h5>
                    </div>
                    <div class="card-body">
                        <form method="post" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                            <div class="form-group">
                                <label for="to">Recipient Email</label>
                                <input type="email" name="to" id="to" class="form-control" value="<?php echo htmlspecialchars($to); ?>" required>
                                <small class="form-text text-muted">Enter your email address to receive the test email</small>
                            </div>
                            
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary"><i class="fas fa-paper-plane"></i> Send Test Email</button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-cog"></i> Current Email Configuration</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        // Get the current configuration values
                        $configFile = __DIR__ . '/includes/mailer_config.php';
                        $configContent = file_get_contents($configFile);
                        
                        // Extract the configuration values
                        preg_match("/define\('SMTP_HOST', '(.*?)'\);/", $configContent, $matches);
                        $smtpHost = $matches[1] ?? 'Not set';
                        
                        preg_match("/define\('SMTP_PORT', (\d+)\);/", $configContent, $matches);
                        $smtpPort = $matches[1] ?? 'Not set';
                        
                        preg_match("/define\('SMTP_SECURE', '(.*?)'\);/", $configContent, $matches);
                        $smtpSecure = $matches[1] ?? 'Not set';
                        
                        preg_match("/define\('SMTP_AUTH', (true|false)\);/", $configContent, $matches);
                        $smtpAuth = ($matches[1] ?? 'false') === 'true' ? 'Yes' : 'No';
                        
                        preg_match("/define\('SMTP_USERNAME', '(.*?)'\);/", $configContent, $matches);
                        $smtpUsername = $matches[1] ?? 'Not set';
                        
                        preg_match("/define\('MAIL_FROM_EMAIL', (.*?)\);/", $configContent, $matches);
                        $mailFromEmail = str_replace("'", "", $matches[1] ?? APP_EMAIL);
                        
                        preg_match("/define\('MAIL_FROM_NAME', (.*?)\);/", $configContent, $matches);
                        $mailFromName = str_replace("'", "", $matches[1] ?? APP_NAME);
                        ?>
                        
                        <table class="table table-sm">
                            <tr>
                                <th>SMTP Host:</th>
                                <td><?php echo htmlspecialchars($smtpHost); ?></td>
                            </tr>
                            <tr>
                                <th>SMTP Port:</th>
                                <td><?php echo htmlspecialchars($smtpPort); ?></td>
                            </tr>
                            <tr>
                                <th>Security:</th>
                                <td><?php echo htmlspecialchars($smtpSecure ? $smtpSecure : 'None'); ?></td>
                            </tr>
                            <tr>
                                <th>Authentication:</th>
                                <td><?php echo $smtpAuth; ?></td>
                            </tr>
                            <tr>
                                <th>Username:</th>
                                <td><?php echo htmlspecialchars($smtpUsername ? $smtpUsername : 'Not set'); ?></td>
                            </tr>
                            <tr>
                                <th>From Email:</th>
                                <td><?php echo htmlspecialchars($mailFromEmail); ?></td>
                            </tr>
                            <tr>
                                <th>From Name:</th>
                                <td><?php echo htmlspecialchars($mailFromName); ?></td>
                            </tr>
                        </table>
                        
                        <a href="email_settings.php" class="btn btn-sm btn-secondary mt-2"><i class="fas fa-cog"></i> Change Settings</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-history"></i> Email Log</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        $logFile = __DIR__ . '/logs/email_log.txt';
                        if (file_exists($logFile)) {
                            $logContent = file_get_contents($logFile);
                            if (!empty($logContent)) {
                                echo '<div class="email-log">' . htmlspecialchars($logContent) . '</div>';
                            } else {
                                echo '<p class="text-muted">Email log is empty.</p>';
                            }
                        } else {
                            echo '<p class="text-muted">Email log file does not exist.</p>';
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</body>
</html>
