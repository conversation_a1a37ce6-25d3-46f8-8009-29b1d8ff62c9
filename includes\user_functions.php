<?php
/**
 * User Management Functions
 *
 * This file contains functions related to user management.
 */

require_once 'config.php';

/**
 * Function to get all users
 * Only admin can access all users
 *
 * @return array|string Array of users if successful, error message otherwise
 */
function getAllUsers() {
    global $pdo;

    // Only admin can get all users
    if (!isAdmin()) {
        return "Only administrators can access all user accounts.";
    }

    try {
        $stmt = $pdo->prepare("
            SELECT u.user_id, u.username, u.email, u.first_name, u.last_name, u.gender, u.birthday, u.phone_number,
                   u.role_id, u.is_active, u.created_at, u.updated_at, r.role_name
            FROM users u
            JOIN roles r ON u.role_id = r.role_id
            ORDER BY u.user_id
        ");
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve users: " . $e->getMessage();
    }
}

/**
 * Function to get a user by ID
 * Admin can get any user, users can only get their own profile
 *
 * @param int $userId The user ID
 * @return array|string User data if successful, error message otherwise
 */
function getUserById($userId) {
    global $pdo;

    // Check if user is authorized to get this profile
    if (!isAdmin() && $_SESSION['user_id'] != $userId) {
        // Don't show error on index.php page to avoid disrupting the UI
        $currentPage = basename($_SERVER['PHP_SELF']);
        if ($currentPage === 'index.php') {
            return [
                'user_id' => $userId,
                'username' => 'Unknown',
                'email' => '',
                'first_name' => 'Unknown',
                'last_name' => 'User',
                'role_name' => 'unknown'
            ];
        }
        return "You are not authorized to access this user profile.";
    }

    try {
        $stmt = $pdo->prepare("
            SELECT u.user_id, u.username, u.email, u.first_name, u.last_name, u.gender, u.birthday, u.phone_number,
                   u.profile_picture, u.role_id, u.is_active, u.created_at, u.updated_at, r.role_name
            FROM users u
            JOIN roles r ON u.role_id = r.role_id
            WHERE u.user_id = :userId
        ");
        $stmt->bindParam(':userId', $userId);
        $stmt->execute();

        if ($stmt->rowCount() == 1) {
            return $stmt->fetch();
        } else {
            return "User not found.";
        }
    } catch (PDOException $e) {
        return "Failed to retrieve user: " . $e->getMessage();
    }
}

/**
 * Function to get all roles
 *
 * @return array|string Array of roles if successful, error message otherwise
 */
function getAllRoles() {
    // Return hardcoded roles since we no longer have a roles table
    // For admin user creation, we only return admin and teacher roles (no student)
    $currentPage = basename($_SERVER['PHP_SELF']);
    if ($currentPage === 'users.php' || $currentPage === 'user_add.php') {
        return [
            ['role_id' => 'admin', 'role_name' => 'admin', 'description' => 'Administrator with full system access'],
            ['role_id' => 'teacher', 'role_name' => 'instructor', 'description' => 'Instructor who can create and manage courses']
        ];
    } else {
        // Return all roles for other pages
        return [
            ['role_id' => 'admin', 'role_name' => 'admin', 'description' => 'Administrator with full system access'],
            ['role_id' => 'teacher', 'role_name' => 'instructor', 'description' => 'Instructor who can create and manage courses'],
            ['role_id' => 'student', 'role_name' => 'student', 'description' => 'Student who can enroll in courses']
        ];
    }
}

/**
 * Function to get a role by ID
 *
 * @param string $roleId The role ID (admin, teacher, student)
 * @return array|string Role data if successful, error message otherwise
 */
function getRoleById($roleId) {
    $roles = getAllRoles();

    foreach ($roles as $role) {
        if ($role['role_id'] === $roleId) {
            return $role;
        }
    }

    return "Role not found.";
}

/**
 * Function to count users by role
 * Only admin can access this information
 *
 * @return array|string Array of user counts by role if successful, error message otherwise
 */
function countUsersByRole() {
    global $pdo;

    // Only admin can get user counts
    if (!isAdmin()) {
        return "Only administrators can access user statistics.";
    }

    try {
        $stmt = $pdo->prepare("
            SELECT r.role_name, COUNT(u.user_id) as user_count
            FROM roles r
            LEFT JOIN users u ON r.role_id = u.role_id
            GROUP BY r.role_id, r.role_name
            ORDER BY r.role_id
        ");
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve user counts: " . $e->getMessage();
    }
}

/**
 * Function to search users by name or email
 * Only admin can search all users
 *
 * @param string $searchTerm The search term
 * @return array|string Array of matching users if successful, error message otherwise
 */
function searchUsers($searchTerm) {
    global $pdo;

    // Only admin can search all users
    if (!isAdmin()) {
        return "Only administrators can search user accounts.";
    }

    try {
        $searchTerm = "%$searchTerm%";

        $stmt = $pdo->prepare("
            SELECT u.user_id, u.username, u.email, u.first_name, u.last_name, u.role_id, u.is_active, r.role_name
            FROM users u
            JOIN roles r ON u.role_id = r.role_id
            WHERE u.username LIKE :searchTerm
               OR u.email LIKE :searchTerm
               OR u.first_name LIKE :searchTerm
               OR u.last_name LIKE :searchTerm
            ORDER BY u.user_id
        ");
        $stmt->bindParam(':searchTerm', $searchTerm);
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to search users: " . $e->getMessage();
    }
}

/**
 * Function to get active users
 * Only admin can access this information
 *
 * @return array|string Array of active users if successful, error message otherwise
 */
function getActiveUsers() {
    global $pdo;

    // Only admin can get active users
    if (!isAdmin()) {
        return "Only administrators can access active user information.";
    }

    try {
        $stmt = $pdo->prepare("
            SELECT u.user_id, u.username, u.email, u.first_name, u.last_name, r.role_name
            FROM users u
            JOIN roles r ON u.role_id = r.role_id
            WHERE u.is_active = 1
            ORDER BY u.user_id
        ");
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve active users: " . $e->getMessage();
    }
}

/**
 * Function to get inactive users
 * Only admin can access this information
 *
 * @return array|string Array of inactive users if successful, error message otherwise
 */
function getInactiveUsers() {
    global $pdo;

    // Only admin can get inactive users
    if (!isAdmin()) {
        return "Only administrators can access inactive user information.";
    }

    try {
        $stmt = $pdo->prepare("
            SELECT u.user_id, u.username, u.email, u.first_name, u.last_name, r.role_name
            FROM users u
            JOIN roles r ON u.role_id = r.role_id
            WHERE u.is_active = 0
            ORDER BY u.user_id
        ");
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve inactive users: " . $e->getMessage();
    }
}

/**
 * Function to activate or deactivate a user
 * Only admin can activate/deactivate users
 *
 * @param int $userId The user ID
 * @param bool $isActive The active status
 * @return bool|string True if successful, error message otherwise
 */
function setUserActiveStatus($userId, $isActive) {
    global $pdo;

    // Only admin can activate/deactivate users
    if (!isAdmin()) {
        return "Only administrators can activate or deactivate user accounts.";
    }

    try {
        $stmt = $pdo->prepare("UPDATE users SET is_active = :isActive WHERE user_id = :userId");
        $stmt->bindParam(':isActive', $isActive, PDO::PARAM_BOOL);
        $stmt->bindParam(':userId', $userId);
        $stmt->execute();

        if ($stmt->rowCount() == 1) {
            return true;
        } else {
            return "User not found or status already set.";
        }
    } catch (PDOException $e) {
        return "Failed to update user status: " . $e->getMessage();
    }
}

/**
 * Function to get users by role
 * Admin can get any users, teachers can only get students
 *
 * @param string $roleName The role name (admin, teacher, student)
 * @return array|string Array of users if successful, error message otherwise
 */
function getUsersByRole($roleName) {
    global $pdo;

    // Check permissions
    if (!isAdmin() && ($roleName == 'admin' || $roleName == 'teacher')) {
        return "You don't have permission to access this information.";
    }

    try {
        $stmt = $pdo->prepare("
            SELECT u.user_id, u.username, u.email, u.first_name, u.last_name, u.is_active
            FROM users u
            JOIN roles r ON u.role_id = r.role_id
            WHERE r.role_name = :roleName AND u.is_active = 1
            ORDER BY u.last_name, u.first_name
        ");
        $stmt->bindParam(':roleName', $roleName);
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve users by role: " . $e->getMessage();
    }
}
?>
