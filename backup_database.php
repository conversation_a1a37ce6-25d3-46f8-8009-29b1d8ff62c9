<?php
/**
 * Database Backup Script
 * Creates a backup of your database before running cleanup
 */

// Database credentials
define('DB_SERVER', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', '');
define('DB_NAME', 'maxcel_elearning');

echo "<h1>Database Backup Script</h1>";

// Create backup filename with timestamp
$backupFile = 'backup_' . DB_NAME . '_' . date('Y-m-d_H-i-s') . '.sql';

// Create mysqldump command
$command = "C:\\xampp\\mysql\\bin\\mysqldump.exe --user=" . DB_USERNAME . " --password=" . DB_PASSWORD . " --host=" . DB_SERVER . " " . DB_NAME . " > " . $backupFile;

echo "<p>Creating backup of database '" . DB_NAME . "'...</p>";
echo "<p>Backup file: <strong>$backupFile</strong></p>";

// Execute the backup command
$output = [];
$returnVar = 0;
exec($command, $output, $returnVar);

if ($returnVar === 0) {
    echo "<p style='color: green; font-weight: bold;'>✓ Database backup created successfully!</p>";
    echo "<p>Backup saved as: <strong>$backupFile</strong></p>";
    echo "<p>File size: " . formatBytes(filesize($backupFile)) . "</p>";
    echo "<div style='background: #d4edda; padding: 10px; border: 1px solid #c3e6cb; margin: 10px 0;'>";
    echo "<strong>✓ Backup Complete!</strong><br>";
    echo "You can now safely run the database cleanup script.<br>";
    echo "If anything goes wrong, you can restore from this backup.";
    echo "</div>";
} else {
    echo "<p style='color: red; font-weight: bold;'>✗ Backup failed!</p>";
    echo "<p>Please check your MySQL installation and try again.</p>";
    echo "<p>Error output:</p>";
    echo "<pre>" . implode("\n", $output) . "</pre>";
}

function formatBytes($size, $precision = 2) {
    $base = log($size, 1024);
    $suffixes = array('B', 'KB', 'MB', 'GB', 'TB');
    return round(pow(1024, $base - floor($base)), $precision) . ' ' . $suffixes[floor($base)];
}
?>
