<?php
/**
 * Activity Functions
 *
 * This file contains functions related to activities, quizzes, and other course materials.
 */

require_once 'config.php';
require_once 'course_functions.php';

/**
 * Function to create a new activity
 * Admin can create activities for any course, teachers can only create activities for their own courses
 *
 * @param int $courseId The course ID
 * @param string $title The activity title
 * @param string $description The activity description
 * @param string $activityType The activity type (material, assignment, quiz, question, activity)
 * @param int $points The maximum points (0 for materials)
 * @param string $dueDate The due date (YYYY-MM-DD HH:MM:SS) or null if no due date
 * @param bool $isPublished Whether the activity is published
 * @param int $createdBy The user ID of the creator
 * @param bool $allowLateSubmissions Whether to allow late submissions
 * @param array $files Optional array of uploaded files
 * @return int|string Activity ID if creation successful, error message otherwise
 */
function createActivity($courseId, $title, $description, $activityType, $points = 0, $dueDate = null, $isPublished = true, $createdBy, $allowLateSubmissions = false, $files = null) {
    global $pdo;

    try {
        // Check if user is authorized to create activities for this course
        if (!isAdmin()) {
            $stmt = $pdo->prepare("
                SELECT c.created_by
                FROM courses c
                LEFT JOIN course_instructors ci ON c.course_id = ci.course_id
                WHERE c.course_id = :courseId AND (c.created_by = :userId OR ci.instructor_id = :instructorId)
            ");
            $stmt->bindParam(':courseId', $courseId);
            $stmt->bindParam(':userId', $createdBy);
            $stmt->bindParam(':instructorId', $createdBy);
            $stmt->execute();

            if ($stmt->rowCount() == 0) {
                return "You are not authorized to create activities for this course.";
            }
        }

        // Start transaction
        $pdo->beginTransaction();

        // Create the activity
        $stmt = $pdo->prepare("
            INSERT INTO activities (
                course_id,
                title,
                description,
                activity_type,
                points,
                due_date,
                is_published,
                created_by,
                allow_late_submissions
            )
            VALUES (
                :courseId,
                :title,
                :description,
                :activityType,
                :points,
                :dueDate,
                :isPublished,
                :createdBy,
                :allowLateSubmissions
            )
        ");

        $stmt->bindParam(':courseId', $courseId);
        $stmt->bindParam(':title', $title);
        $stmt->bindParam(':description', $description);
        $stmt->bindParam(':activityType', $activityType);
        $stmt->bindParam(':points', $points);
        $stmt->bindParam(':dueDate', $dueDate);
        $isPublishedInt = $isPublished ? 1 : 0;
        $stmt->bindParam(':isPublished', $isPublishedInt);
        $stmt->bindParam(':createdBy', $createdBy);
        $allowLateSubmissionsInt = $allowLateSubmissions ? 1 : 0;
        $stmt->bindParam(':allowLateSubmissions', $allowLateSubmissionsInt);

        $stmt->execute();
        $activityId = $pdo->lastInsertId();

        // Handle file uploads if provided
        if ($files && !empty($files['name'][0])) {
            $uploadDir = 'uploads/activities/' . $activityId . '/';

            // Create directory if it doesn't exist
            if (!file_exists($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }

            // Process each file
            for ($i = 0; $i < count($files['name']); $i++) {
                if ($files['error'][$i] == 0) {
                    $fileName = basename($files['name'][$i]);
                    $fileSize = $files['size'][$i];
                    $fileType = $files['type'][$i];
                    $fileTmpName = $files['tmp_name'][$i];

                    // Generate unique filename
                    $uniqueName = uniqid() . '_' . $fileName;
                    $targetFilePath = $uploadDir . $uniqueName;

                    // Move the file
                    if (move_uploaded_file($fileTmpName, $targetFilePath)) {
                        // Save file info to database
                        $stmt = $pdo->prepare("
                            INSERT INTO activity_files (
                                activity_id,
                                file_name,
                                file_path,
                                file_size,
                                file_type,
                                uploaded_by
                            )
                            VALUES (
                                :activityId,
                                :fileName,
                                :filePath,
                                :fileSize,
                                :fileType,
                                :uploadedBy
                            )
                        ");

                        $stmt->bindParam(':activityId', $activityId);
                        $stmt->bindParam(':fileName', $fileName);
                        $stmt->bindParam(':filePath', $targetFilePath);
                        $stmt->bindParam(':fileSize', $fileSize);
                        $stmt->bindParam(':fileType', $fileType);
                        $stmt->bindParam(':uploadedBy', $createdBy);
                        $stmt->execute();
                    }
                }
            }
        }

        // Commit transaction
        $pdo->commit();

        // Send notifications to enrolled students if the activity is published
        if ($isPublished) {
            // Get all enrolled students for this course
            $enrolledStudentsStmt = $pdo->prepare("
                SELECT u.user_id, u.username
                FROM enrollments e
                JOIN users u ON e.user_id = u.user_id
                WHERE e.course_id = :course_id AND e.is_active = 1
            ");
            $enrolledStudentsStmt->execute([':course_id' => $courseId]);
            $enrolledStudents = $enrolledStudentsStmt->fetchAll();

            // Get course name
            $courseStmt = $pdo->prepare("SELECT title FROM courses WHERE course_id = :course_id");
            $courseStmt->execute([':course_id' => $courseId]);
            $courseName = $courseStmt->fetchColumn();

            // Get creator name
            $creatorStmt = $pdo->prepare("SELECT username FROM users WHERE user_id = :user_id");
            $creatorStmt->execute([':user_id' => $createdBy]);
            $creatorName = $creatorStmt->fetchColumn();

            // Determine notification type based on activity type
            $notificationType = 'activity_created';
            if ($activityType === 'assignment') {
                $notificationType = 'assignment_created';
            } elseif ($activityType === 'quiz') {
                $notificationType = 'quiz_created';
            }

            // Create notification for each enrolled student
            if (function_exists('createNotification')) {
                foreach ($enrolledStudents as $student) {
                    $notificationTitle = "New " . ucfirst($activityType) . " Added";
                    $notificationMessage = "$creatorName added a new " . strtolower($activityType) . " '$title' to the course '$courseName'";

                    if ($dueDate) {
                        $notificationMessage .= " due on " . date('F j, Y g:i A', strtotime($dueDate));
                    }

                    createNotification($student['user_id'], $notificationTitle, $notificationMessage, $notificationType, $activityId);
                }
            }
        }

        return $activityId;
    } catch (PDOException $e) {
        // Rollback transaction on error
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return "Failed to create activity: " . $e->getMessage();
    }
}

/**
 * Function to update an activity
 * Admin can update any activity, teachers can only update activities in their own courses
 *
 * @param int $activityId The activity ID
 * @param string $title The activity title
 * @param string $description The activity description
 * @param int $points The maximum points
 * @param string $dueDate The due date (YYYY-MM-DD HH:MM:SS) or null if no due date
 * @param bool $isPublished Whether the activity is published
 * @param bool $allowLateSubmissions Whether to allow late submissions
 * @param array $files Optional array of uploaded files
 * @return bool|string True if update successful, error message otherwise
 */
function updateActivity($activityId, $title, $description, $points = null, $dueDate = null, $isPublished = null, $allowLateSubmissions = null, $files = null) {
    global $pdo;

    try {
        // Check if user is authorized to update this activity
        if (!isAdmin()) {
            $stmt = $pdo->prepare("
                SELECT a.activity_id, c.created_by
                FROM activities a
                JOIN courses c ON a.course_id = c.course_id
                LEFT JOIN course_instructors ci ON c.course_id = ci.course_id
                WHERE a.activity_id = :activityId AND (c.created_by = :userId OR ci.instructor_id = :instructorId)
            ");
            $stmt->bindParam(':activityId', $activityId);
            $stmt->bindParam(':userId', $_SESSION['user_id']);
            $stmt->bindParam(':instructorId', $_SESSION['user_id']);
            $stmt->execute();

            if ($stmt->rowCount() == 0) {
                return "You are not authorized to update this activity.";
            }
        }

        // Start building the query
        $query = "UPDATE activities SET title = :title, description = :description";
        $params = [
            ':title' => $title,
            ':description' => $description,
            ':activityId' => $activityId
        ];

        // Add optional parameters if provided
        if ($points !== null) {
            $query .= ", points = :points";
            $params[':points'] = $points;
        }

        if ($dueDate !== null) {
            $query .= ", due_date = :dueDate";
            $params[':dueDate'] = $dueDate;
        }

        if ($isPublished !== null) {
            $query .= ", is_published = :isPublished";
            $isPublishedInt = $isPublished ? 1 : 0;
            $params[':isPublished'] = $isPublishedInt;
        }

        if ($allowLateSubmissions !== null) {
            $query .= ", allow_late_submissions = :allowLateSubmissions";
            $allowLateSubmissionsInt = $allowLateSubmissions ? 1 : 0;
            $params[':allowLateSubmissions'] = $allowLateSubmissionsInt;
        }

        $query .= " WHERE activity_id = :activityId";

        // Get activity details before update for notification
        $activityStmt = $pdo->prepare("
            SELECT a.*, c.title as course_title, u.username as creator_name
            FROM activities a
            JOIN courses c ON a.course_id = c.course_id
            JOIN users u ON a.created_by = u.user_id
            WHERE a.activity_id = :activityId
        ");
        $activityStmt->bindParam(':activityId', $activityId);
        $activityStmt->execute();
        $activity = $activityStmt->fetch(PDO::FETCH_ASSOC);

        // Execute the query
        $stmt = $pdo->prepare($query);
        $stmt->execute($params);

        // Send notifications if the activity is published or being published
        if (($isPublished !== null && $isPublished) || ($isPublished === null && $activity['is_published'])) {
            // Get all enrolled students for this course
            $enrolledStudentsStmt = $pdo->prepare("
                SELECT u.user_id, u.username
                FROM enrollments e
                JOIN users u ON e.user_id = u.user_id
                WHERE e.course_id = :course_id AND e.is_active = 1
            ");
            $enrolledStudentsStmt->execute([':course_id' => $activity['course_id']]);
            $enrolledStudents = $enrolledStudentsStmt->fetchAll();

            // Determine notification type based on activity type
            $notificationType = 'activity_updated';
            if ($activity['activity_type'] === 'assignment') {
                $notificationType = 'assignment_updated';
            } elseif ($activity['activity_type'] === 'quiz') {
                $notificationType = 'quiz_updated';
            }

            // Create notification for each enrolled student
            if (function_exists('createNotification')) {
                foreach ($enrolledStudents as $student) {
                    $notificationTitle = ucfirst($activity['activity_type']) . " Updated";
                    $notificationMessage = $activity['creator_name'] . " updated the " . strtolower($activity['activity_type']) . " '$title' in the course '" . $activity['course_title'] . "'";

                    if ($dueDate) {
                        $notificationMessage .= ". Due date: " . date('F j, Y g:i A', strtotime($dueDate));
                    }

                    createNotification($student['user_id'], $notificationTitle, $notificationMessage, $notificationType, $activityId);
                }
            }
        }

        // Handle file uploads if provided
        if ($files && !empty($files['name'][0])) {
            $uploadDir = 'uploads/activities/' . $activityId . '/';

            // Create directory if it doesn't exist
            if (!file_exists($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }

            // Process each file
            foreach ($files['name'] as $key => $fileName) {
                if ($files['error'][$key] === 0) {
                    $fileTmpName = $files['tmp_name'][$key];
                    $fileType = $files['type'][$key];
                    $fileSize = $files['size'][$key];

                    // Generate unique filename
                    $uniqueName = uniqid() . '_' . $fileName;
                    $targetFilePath = $uploadDir . $uniqueName;

                    // Move the file
                    if (move_uploaded_file($fileTmpName, $targetFilePath)) {
                        // Save file info to database
                        $stmt = $pdo->prepare("
                            INSERT INTO activity_files (activity_id, file_name, file_path, file_type, file_size, uploaded_by)
                            VALUES (:activityId, :fileName, :filePath, :fileType, :fileSize, :uploadedBy)
                        ");
                        $stmt->bindParam(':activityId', $activityId);
                        $stmt->bindParam(':fileName', $fileName);
                        $stmt->bindParam(':filePath', $targetFilePath);
                        $stmt->bindParam(':fileType', $fileType);
                        $stmt->bindParam(':fileSize', $fileSize);
                        $stmt->bindParam(':uploadedBy', $_SESSION['user_id']);
                        $stmt->execute();
                    }
                }
            }
        }

        return true;
    } catch (PDOException $e) {
        return "Failed to update activity: " . $e->getMessage();
    }
}

/**
 * Function to delete an activity
 * Admin can delete any activity, teachers can only delete activities in their own courses
 *
 * @param int $activityId The activity ID
 * @return bool|string True if deletion successful, error message otherwise
 */
function deleteActivity($activityId) {
    global $pdo;

    try {
        // Check if user is authorized to delete this activity
        if (!isAdmin()) {
            $stmt = $pdo->prepare("
                SELECT a.activity_id, c.created_by
                FROM activities a
                JOIN courses c ON a.course_id = c.course_id
                LEFT JOIN course_instructors ci ON c.course_id = ci.course_id
                WHERE a.activity_id = :activityId AND (c.created_by = :userId OR ci.instructor_id = :instructorId)
            ");
            $stmt->bindParam(':activityId', $activityId);
            $stmt->bindParam(':userId', $_SESSION['user_id']);
            $stmt->bindParam(':instructorId', $_SESSION['user_id']);
            $stmt->execute();

            if ($stmt->rowCount() == 0) {
                return "You are not authorized to delete this activity.";
            }
        }

        // Delete the activity
        $stmt = $pdo->prepare("DELETE FROM activities WHERE activity_id = :activityId");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->execute();

        if ($stmt->rowCount() == 1) {
            return true;
        } else {
            return "Activity not found.";
        }
    } catch (PDOException $e) {
        return "Failed to delete activity: " . $e->getMessage();
    }
}

/**
 * Function to get an activity by ID
 *
 * @param int $activityId The activity ID
 * @return array|string Activity data if successful, error message otherwise
 */
function getActivityById($activityId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT a.*, u.username as creator_name, c.title as course_title
            FROM activities a
            JOIN users u ON a.created_by = u.user_id
            JOIN courses c ON a.course_id = c.course_id
            WHERE a.activity_id = :activityId
        ");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->execute();

        if ($stmt->rowCount() == 1) {
            return $stmt->fetch();
        } else {
            return "Activity not found.";
        }
    } catch (PDOException $e) {
        return "Failed to retrieve activity: " . $e->getMessage();
    }
}

/**
 * Function to get all activities for a course
 *
 * @param int $courseId The course ID
 * @param bool $publishedOnly Whether to get only published activities
 * @return array|string Array of activities if successful, error message otherwise
 */
function getActivitiesByCourse($courseId, $publishedOnly = true) {
    global $pdo;

    try {
        $query = "
            SELECT a.*, u.username as creator_name
            FROM activities a
            JOIN users u ON a.created_by = u.user_id
            WHERE a.course_id = :courseId
        ";

        if ($publishedOnly) {
            $query .= " AND a.is_published = 1";
        }

        $query .= " ORDER BY a.created_at DESC";

        $stmt = $pdo->prepare($query);
        $stmt->bindParam(':courseId', $courseId);
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve activities: " . $e->getMessage();
    }
}

/**
 * Function to add a question to an activity
 *
 * @param int $activityId The activity ID
 * @param string $questionText The question text
 * @param string $questionType The question type (multiple_choice, true_false, short_answer)
 * @param int $points The points for this question
 * @param int $position The position of the question in the activity
 * @return int|string Question ID if creation successful, error message otherwise
 */
function addActivityQuestion($activityId, $questionText, $questionType, $points = 1, $position = 0) {
    global $pdo;

    error_log("addActivityQuestion called with: activityId=$activityId, questionText=$questionText, questionType=$questionType, points=$points");

    try {
        // Check if the activity exists
        $stmt = $pdo->prepare("
            SELECT activity_id, activity_type, created_by
            FROM activities
            WHERE activity_id = :activityId
        ");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->execute();

        error_log("Activity check query executed");

        if ($stmt->rowCount() == 0) {
            return "Activity not found.";
        }

        $activity = $stmt->fetch();

        // Check if user is authorized to add questions to this activity
        if (!isAdmin() && $activity['created_by'] != $_SESSION['user_id']) {
            // Check if user is an instructor for this course
            $stmt = $pdo->prepare("
                SELECT ci.instructor_id
                FROM activities a
                JOIN courses c ON a.course_id = c.course_id
                JOIN course_instructors ci ON c.course_id = ci.course_id
                WHERE a.activity_id = :activityId AND ci.instructor_id = :userId
            ");
            $stmt->bindParam(':activityId', $activityId);
            $stmt->bindParam(':userId', $_SESSION['user_id']);
            $stmt->execute();

            if ($stmt->rowCount() == 0) {
                return "You are not authorized to add questions to this activity.";
            }
        }

        // Add the question
        $stmt = $pdo->prepare("
            INSERT INTO activity_questions (activity_id, question_text, question_type, points, position)
            VALUES (:activityId, :questionText, :questionType, :points, :position)
        ");

        $stmt->bindParam(':activityId', $activityId);
        $stmt->bindParam(':questionText', $questionText);
        $stmt->bindParam(':questionType', $questionType);
        $stmt->bindParam(':points', $points);
        $stmt->bindParam(':position', $position);

        error_log("Executing SQL: INSERT INTO activity_questions (activity_id, question_text, question_type, points, position) VALUES ($activityId, '$questionText', '$questionType', $points, $position)");

        try {
            $stmt->execute();
            error_log("SQL executed successfully");
            $lastId = $pdo->lastInsertId();
            error_log("Last inserted ID: $lastId");
            return $lastId;
        } catch (PDOException $e) {
            error_log("SQL execution failed: " . $e->getMessage());
            throw $e;
        }
    } catch (PDOException $e) {
        return "Failed to add question: " . $e->getMessage();
    }
}

/**
 * Function to add an option to a multiple choice or true/false question
 *
 * @param int $questionId The question ID
 * @param string $optionText The option text
 * @param bool $isCorrect Whether this option is correct
 * @param int $position The position of the option in the question
 * @return int|string Option ID if creation successful, error message otherwise
 */
function addActivityQuestionOption($questionId, $optionText, $isCorrect = false, $position = 0) {
    global $pdo;

    error_log("addActivityQuestionOption called with: questionId=$questionId, optionText=$optionText, isCorrect=" . ($isCorrect ? "true" : "false"));

    try {
        // Check if the question exists and is a multiple choice or true/false question
        $stmt = $pdo->prepare("
            SELECT q.question_id, q.question_type, a.created_by
            FROM activity_questions q
            JOIN activities a ON q.activity_id = a.activity_id
            WHERE q.question_id = :questionId
        ");
        $stmt->bindParam(':questionId', $questionId);
        $stmt->execute();

        error_log("Question check query executed");

        if ($stmt->rowCount() == 0) {
            return "Question not found.";
        }

        $question = $stmt->fetch();

        // Allow options for multiple_choice, true_false, and short_answer
        $allowedTypes = ['multiple_choice', 'true_false', 'short_answer'];
        if (!in_array($question['question_type'], $allowedTypes)) {
            return "This question type does not support options.";
        }

        // Check if user is authorized to add options to this question
        if (!isAdmin() && $question['created_by'] != $_SESSION['user_id']) {
            // Check if user is an instructor for this course
            $stmt = $pdo->prepare("
                SELECT ci.instructor_id
                FROM activity_questions q
                JOIN activities a ON q.activity_id = a.activity_id
                JOIN courses c ON a.course_id = c.course_id
                JOIN course_instructors ci ON c.course_id = ci.course_id
                WHERE q.question_id = :questionId AND ci.instructor_id = :userId
            ");
            $stmt->bindParam(':questionId', $questionId);
            $stmt->bindParam(':userId', $_SESSION['user_id']);
            $stmt->execute();

            if ($stmt->rowCount() == 0) {
                return "You are not authorized to add options to this question.";
            }
        }

        // Add the option
        $stmt = $pdo->prepare("
            INSERT INTO activity_question_options (question_id, option_text, is_correct, position)
            VALUES (:questionId, :optionText, :isCorrect, :position)
        ");

        $stmt->bindParam(':questionId', $questionId);
        $stmt->bindParam(':optionText', $optionText);
        $isCorrectInt = $isCorrect ? 1 : 0;
        $stmt->bindParam(':isCorrect', $isCorrectInt);
        $stmt->bindParam(':position', $position);

        error_log("Executing SQL: INSERT INTO activity_question_options (question_id, option_text, is_correct, position) VALUES ($questionId, '$optionText', $isCorrectInt, $position)");

        try {
            $stmt->execute();
            error_log("SQL executed successfully");
            $lastId = $pdo->lastInsertId();
            error_log("Last inserted ID: $lastId");
            return $lastId;
        } catch (PDOException $e) {
            error_log("SQL execution failed: " . $e->getMessage());
            throw $e;
        }
    } catch (PDOException $e) {
        return "Failed to add option: " . $e->getMessage();
    }
}

/**
 * Function to get all questions for an activity
 *
 * @param int $activityId The activity ID
 * @return array|string Array of questions if successful, error message otherwise
 */
function getActivityQuestions($activityId) {
    global $pdo;

    try {
        error_log("getActivityQuestions called for activity ID: $activityId");

        // Check if the activity_questions table exists
        try {
            $tableCheck = $pdo->query("SHOW TABLES LIKE 'activity_questions'");
            if ($tableCheck->rowCount() == 0) {
                error_log("Table 'activity_questions' does not exist!");
                return "Table 'activity_questions' does not exist. Please check your database setup.";
            }
        } catch (PDOException $tableCheckError) {
            error_log("Error checking for activity_questions table: " . $tableCheckError->getMessage());
        }

        $stmt = $pdo->prepare("
            SELECT * FROM activity_questions
            WHERE activity_id = :activityId
            ORDER BY position, question_id
        ");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->execute();

        $questions = $stmt->fetchAll(PDO::FETCH_ASSOC);

        error_log("Raw questions from database for activity $activityId: " . print_r($questions, true));

        // Get options for each question
        foreach ($questions as &$question) {
            if ($question['question_type'] == 'multiple_choice' || $question['question_type'] == 'true_false' || $question['question_type'] == 'short_answer') {
                // Check if the activity_question_options table exists
                try {
                    $tableCheck = $pdo->query("SHOW TABLES LIKE 'activity_question_options'");
                    if ($tableCheck->rowCount() == 0) {
                        error_log("Table 'activity_question_options' does not exist!");
                        $question['options'] = [];
                        continue;
                    }
                } catch (PDOException $tableCheckError) {
                    error_log("Error checking for activity_question_options table: " . $tableCheckError->getMessage());
                }

                $stmt = $pdo->prepare("
                    SELECT * FROM activity_question_options
                    WHERE question_id = :questionId
                    ORDER BY position, option_id
                ");
                $stmt->bindParam(':questionId', $question['question_id']);
                $stmt->execute();

                $question['options'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
                error_log("Options for question ID " . $question['question_id'] . ": " . print_r($question['options'], true));
            } else {
                $question['options'] = [];
            }
        }

        return $questions;
    } catch (PDOException $e) {
        $errorMsg = "Failed to retrieve activity questions: " . $e->getMessage();
        error_log($errorMsg);
        return $errorMsg;
    }
}

/**
 * Function to update an activity question
 *
 * @param int $questionId The question ID
 * @param string $questionText The question text
 * @param int $points The points for this question
 * @param int $position The position of the question in the activity
 * @return bool|string True if update successful, error message otherwise
 */
function updateActivityQuestion($questionId, $questionText, $points = null, $position = null) {
    global $pdo;

    try {
        // Check if the question exists
        $stmt = $pdo->prepare("
            SELECT q.question_id, a.created_by
            FROM activity_questions q
            JOIN activities a ON q.activity_id = a.activity_id
            WHERE q.question_id = :questionId
        ");
        $stmt->bindParam(':questionId', $questionId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "Question not found.";
        }

        $question = $stmt->fetch();

        // Check if user is authorized to update this question
        if (!isAdmin() && $question['created_by'] != $_SESSION['user_id']) {
            // Check if user is an instructor for this course
            $stmt = $pdo->prepare("
                SELECT ci.instructor_id
                FROM activity_questions q
                JOIN activities a ON q.activity_id = a.activity_id
                JOIN courses c ON a.course_id = c.course_id
                JOIN course_instructors ci ON c.course_id = ci.course_id
                WHERE q.question_id = :questionId AND ci.instructor_id = :userId
            ");
            $stmt->bindParam(':questionId', $questionId);
            $stmt->bindParam(':userId', $_SESSION['user_id']);
            $stmt->execute();

            if ($stmt->rowCount() == 0) {
                return "You are not authorized to update this question.";
            }
        }

        // Start building the query
        $query = "UPDATE activity_questions SET question_text = :questionText";
        $params = [
            ':questionText' => $questionText,
            ':questionId' => $questionId
        ];

        // Add optional parameters if provided
        if ($points !== null) {
            $query .= ", points = :points";
            $params[':points'] = $points;
        }

        if ($position !== null) {
            $query .= ", position = :position";
            $params[':position'] = $position;
        }

        $query .= " WHERE question_id = :questionId";

        // Execute the query
        $stmt = $pdo->prepare($query);
        $stmt->execute($params);

        return true;
    } catch (PDOException $e) {
        return "Failed to update question: " . $e->getMessage();
    }
}

/**
 * Function to delete an activity question
 *
 * @param int $questionId The question ID
 * @return bool|string True if deletion successful, error message otherwise
 */
function deleteActivityQuestion($questionId) {
    global $pdo;

    try {
        // Check if the question exists
        $stmt = $pdo->prepare("
            SELECT q.question_id, a.created_by
            FROM activity_questions q
            JOIN activities a ON q.activity_id = a.activity_id
            WHERE q.question_id = :questionId
        ");
        $stmt->bindParam(':questionId', $questionId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "Question not found.";
        }

        $question = $stmt->fetch();

        // Check if user is authorized to delete this question
        if (!isAdmin() && $question['created_by'] != $_SESSION['user_id']) {
            // Check if user is an instructor for this course
            $stmt = $pdo->prepare("
                SELECT ci.instructor_id
                FROM activity_questions q
                JOIN activities a ON q.activity_id = a.activity_id
                JOIN courses c ON a.course_id = c.course_id
                JOIN course_instructors ci ON c.course_id = ci.course_id
                WHERE q.question_id = :questionId AND ci.instructor_id = :userId
            ");
            $stmt->bindParam(':questionId', $questionId);
            $stmt->bindParam(':userId', $_SESSION['user_id']);
            $stmt->execute();

            if ($stmt->rowCount() == 0) {
                return "You are not authorized to delete this question.";
            }
        }

        // Delete the question
        $stmt = $pdo->prepare("DELETE FROM activity_questions WHERE question_id = :questionId");
        $stmt->bindParam(':questionId', $questionId);
        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        return "Failed to delete question: " . $e->getMessage();
    }
}

/**
 * Function to create a new quiz
 * Admin can create quizzes for any course, teachers can only create quizzes for their own courses
 *
 * @param int $courseId The course ID
 * @param string $title The quiz title
 * @param string $description The quiz description
 * @param int $points The maximum points
 * @param string $dueDate The due date (YYYY-MM-DD HH:MM:SS) or null if no due date
 * @param int $timeLimit The time limit in minutes
 * @param int $moduleId The module ID (0 for no module)
 * @param bool $allowLateSubmissions Whether to allow late submissions
 * @param bool $isPublished Whether the quiz is published
 * @param int $createdBy The user ID of the creator
 * @param array $files Optional array of uploaded files
 * @return int|string Activity ID if creation successful, error message otherwise
 */
function createQuiz($courseId, $title, $description, $points = 0, $dueDate = null, $timeLimit = 60, $moduleId = 0, $allowLateSubmissions = false, $isPublished = true, $createdBy, $files = null) {
    // Create the activity with type 'quiz'
    $activityId = createActivity($courseId, $title, $description, 'quiz', $points, $dueDate, $isPublished, $createdBy, $allowLateSubmissions, $files);

    if (is_numeric($activityId)) {
        global $pdo;

        try {
            // Add quiz-specific details
            $stmt = $pdo->prepare("
                INSERT INTO quiz_settings (activity_id, time_limit)
                VALUES (:activityId, :timeLimit)
            ");

            $stmt->bindParam(':activityId', $activityId);
            $stmt->bindParam(':timeLimit', $timeLimit);
            $stmt->execute();

            // Add to module if specified
            if ($moduleId > 0) {
                $moduleResult = addActivityToModule($activityId, $moduleId);
                if (is_string($moduleResult)) {
                    return "Quiz created but could not be added to the module: " . $moduleResult;
                }
            }

            return $activityId;
        } catch (PDOException $e) {
            return "Failed to create quiz settings: " . $e->getMessage();
        }
    }

    return $activityId; // Return error message from createActivity
}

/**
 * Function to add an activity to a module
 *
 * @param int $activityId The activity ID
 * @param int $moduleId The module ID
 * @param int $orderNumber The order number in the module
 * @return bool|string True if successful, error message otherwise
 */
function addActivityToModule($activityId, $moduleId, $orderNumber = 0) {
    global $pdo;

    try {
        // Check if the activity exists
        $stmt = $pdo->prepare("SELECT activity_id, course_id FROM activities WHERE activity_id = :activityId");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "Activity not found.";
        }

        $activity = $stmt->fetch();

        // Check if the module exists and belongs to the same course
        $stmt = $pdo->prepare("SELECT module_id, course_id FROM modules WHERE module_id = :moduleId");
        $stmt->bindParam(':moduleId', $moduleId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "Module not found.";
        }

        $module = $stmt->fetch();

        if ($activity['course_id'] != $module['course_id']) {
            return "The module does not belong to the same course as the activity.";
        }

        // Check if the activity is already in the module
        $stmt = $pdo->prepare("
            SELECT * FROM module_activities
            WHERE module_id = :moduleId AND activity_id = :activityId
        ");
        $stmt->bindParam(':moduleId', $moduleId);
        $stmt->bindParam(':activityId', $activityId);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            // Activity already in module, update order
            $stmt = $pdo->prepare("
                UPDATE module_activities
                SET order_number = :orderNumber
                WHERE module_id = :moduleId AND activity_id = :activityId
            ");
        } else {
            // Add activity to module
            $stmt = $pdo->prepare("
                INSERT INTO module_activities (module_id, activity_id, order_number)
                VALUES (:moduleId, :activityId, :orderNumber)
            ");
        }

        $stmt->bindParam(':moduleId', $moduleId);
        $stmt->bindParam(':activityId', $activityId);
        $stmt->bindParam(':orderNumber', $orderNumber);
        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        return "Failed to add activity to module: " . $e->getMessage();
    }
}

/**
 * Function to add a question to a quiz activity
 *
 * @param int $activityId The activity ID (must be a quiz)
 * @param string $questionText The question text
 * @param string $questionType The question type (multiple_choice, short_answer, paragraph, checkbox, dropdown)
 * @param int $points The points for this question
 * @param int $position The position of the question in the quiz
 * @return int|string Question ID if creation successful, error message otherwise
 */
function addQuizActivityQuestion($activityId, $questionText, $questionType, $points = 1, $position = 0) {
    global $pdo;

    error_log("addQuizActivityQuestion called with: activityId=$activityId, questionText=$questionText, questionType=$questionType, points=$points");

    try {
        // Check if the activity exists and is a quiz
        $stmt = $pdo->prepare("
            SELECT activity_id, activity_type, created_by
            FROM activities
            WHERE activity_id = :activityId
        ");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "Activity not found.";
        }

        $activity = $stmt->fetch();

        if ($activity['activity_type'] != 'quiz') {
            return "This activity is not a quiz.";
        }

        // Check if user is authorized to add questions to this quiz
        if (!isAdmin() && $activity['created_by'] != $_SESSION['user_id']) {
            // Check if user is an instructor for this course
            $stmt = $pdo->prepare("
                SELECT ci.instructor_id
                FROM activities a
                JOIN courses c ON a.course_id = c.course_id
                JOIN course_instructors ci ON c.course_id = ci.course_id
                WHERE a.activity_id = :activityId AND ci.instructor_id = :userId
            ");
            $stmt->bindParam(':activityId', $activityId);
            $stmt->bindParam(':userId', $_SESSION['user_id']);
            $stmt->execute();

            if ($stmt->rowCount() == 0) {
                return "You are not authorized to add questions to this quiz.";
            }
        }

        // Add the question
        $stmt = $pdo->prepare("
            INSERT INTO quiz_questions (activity_id, question_text, question_type, points, position)
            VALUES (:activityId, :questionText, :questionType, :points, :position)
        ");

        $stmt->bindParam(':activityId', $activityId);
        $stmt->bindParam(':questionText', $questionText);
        $stmt->bindParam(':questionType', $questionType);
        $stmt->bindParam(':points', $points);
        $stmt->bindParam(':position', $position);

        error_log("Executing SQL: INSERT INTO quiz_questions (activity_id, question_text, question_type, points, position) VALUES ($activityId, '$questionText', '$questionType', $points, $position)");

        try {
            $stmt->execute();
            error_log("SQL executed successfully");
        } catch (PDOException $e) {
            error_log("SQL execution failed: " . $e->getMessage());
            throw $e;
        }

        return $pdo->lastInsertId();
    } catch (PDOException $e) {
        return "Failed to add question: " . $e->getMessage();
    }
}

/**
 * Function to add an option to a multiple choice or checkbox question
 *
 * @param int $questionId The question ID
 * @param string $optionText The option text
 * @param bool $isCorrect Whether this option is correct
 * @param int $position The position of the option in the question
 * @return int|string Option ID if creation successful, error message otherwise
 */
function addQuizOption($questionId, $optionText, $isCorrect = false, $position = 0) {
    global $pdo;

    try {
        // Check if the question exists and is a multiple choice or checkbox question
        $stmt = $pdo->prepare("
            SELECT q.question_id, q.question_type, a.created_by
            FROM quiz_questions q
            JOIN activities a ON q.activity_id = a.activity_id
            WHERE q.question_id = :questionId
        ");
        $stmt->bindParam(':questionId', $questionId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "Question not found.";
        }

        $question = $stmt->fetch();

        // Allow options for multiple_choice, checkbox, dropdown, true_false, and short_answer
        $allowedTypes = ['multiple_choice', 'checkbox', 'dropdown', 'true_false', 'short_answer'];
        if (!in_array($question['question_type'], $allowedTypes)) {
            return "This question type does not support options.";
        }

        // Check if user is authorized to add options to this question
        if (!isAdmin() && $question['created_by'] != $_SESSION['user_id']) {
            // Check if user is an instructor for this course
            $stmt = $pdo->prepare("
                SELECT ci.instructor_id
                FROM quiz_questions q
                JOIN activities a ON q.activity_id = a.activity_id
                JOIN courses c ON a.course_id = c.course_id
                JOIN course_instructors ci ON c.course_id = ci.course_id
                WHERE q.question_id = :questionId AND ci.instructor_id = :userId
            ");
            $stmt->bindParam(':questionId', $questionId);
            $stmt->bindParam(':userId', $_SESSION['user_id']);
            $stmt->execute();

            if ($stmt->rowCount() == 0) {
                return "You are not authorized to add options to this question.";
            }
        }

        // Add the option
        $stmt = $pdo->prepare("
            INSERT INTO quiz_options (question_id, option_text, is_correct, position)
            VALUES (:questionId, :optionText, :isCorrect, :position)
        ");

        $stmt->bindParam(':questionId', $questionId);
        $stmt->bindParam(':optionText', $optionText);
        $isCorrectInt = $isCorrect ? 1 : 0;
        $stmt->bindParam(':isCorrect', $isCorrectInt);
        $stmt->bindParam(':position', $position);

        $stmt->execute();

        return $pdo->lastInsertId();
    } catch (PDOException $e) {
        return "Failed to add option: " . $e->getMessage();
    }
}

/**
 * Function to get all questions for a quiz
 *
 * @param int $activityId The activity ID
 * @return array|string Array of questions if successful, error message otherwise
 */
function getQuizQuestions($activityId) {
    global $pdo;

    try {
        error_log("getQuizQuestions called for activity ID: $activityId");

        // Check if the quiz_questions table exists
        try {
            $tableCheck = $pdo->query("SHOW TABLES LIKE 'quiz_questions'");
            if ($tableCheck->rowCount() == 0) {
                error_log("Table 'quiz_questions' does not exist!");
                return "Table 'quiz_questions' does not exist. Please check your database setup.";
            }
        } catch (PDOException $tableCheckError) {
            error_log("Error checking for quiz_questions table: " . $tableCheckError->getMessage());
        }

        // Get the activity type
        $activityStmt = $pdo->prepare("SELECT activity_type FROM activities WHERE activity_id = :activityId");
        $activityStmt->bindParam(':activityId', $activityId);
        $activityStmt->execute();
        $activityType = $activityStmt->fetchColumn();

        error_log("Activity type for ID $activityId: $activityType");

        $stmt = $pdo->prepare("
            SELECT * FROM quiz_questions
            WHERE activity_id = :activityId
            ORDER BY position, question_id
        ");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->execute();

        $questions = $stmt->fetchAll(PDO::FETCH_ASSOC);

        error_log("Raw quiz questions from database for activity $activityId: " . print_r($questions, true));

        // Get options for each question
        foreach ($questions as &$question) {
            if ($question['question_type'] == 'multiple_choice' || $question['question_type'] == 'checkbox' ||
                $question['question_type'] == 'dropdown' || $question['question_type'] == 'true_false' ||
                $question['question_type'] == 'short_answer') {

                // Check if the quiz_options table exists
                try {
                    $tableCheck = $pdo->query("SHOW TABLES LIKE 'quiz_options'");
                    if ($tableCheck->rowCount() == 0) {
                        error_log("Table 'quiz_options' does not exist!");
                        $question['options'] = [];
                        continue;
                    }
                } catch (PDOException $tableCheckError) {
                    error_log("Error checking for quiz_options table: " . $tableCheckError->getMessage());
                }

                $stmt = $pdo->prepare("
                    SELECT * FROM quiz_options
                    WHERE question_id = :questionId
                    ORDER BY position, option_id
                ");
                $stmt->bindParam(':questionId', $question['question_id']);
                $stmt->execute();

                $question['options'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
                error_log("Options for quiz question ID " . $question['question_id'] . ": " . print_r($question['options'], true));
            } else {
                $question['options'] = [];
            }
        }

        return $questions;
    } catch (PDOException $e) {
        $errorMsg = "Failed to retrieve quiz questions: " . $e->getMessage();
        error_log($errorMsg);
        return $errorMsg;
    }
}

// Assignment questions are now handled by getActivityQuestions
// This specialized function has been removed for consistency

/**
 * Function to update a quiz question
 *
 * @param int $questionId The question ID
 * @param string $questionText The question text
 * @param int $points The points for this question
 * @param int $position The position of the question in the quiz
 * @return bool|string True if update successful, error message otherwise
 */
function updateActivityQuizQuestion($questionId, $questionText, $points = null, $position = null) {
    global $pdo;

    try {
        // Check if the question exists
        $stmt = $pdo->prepare("
            SELECT q.question_id, a.created_by
            FROM quiz_questions q
            JOIN activities a ON q.activity_id = a.activity_id
            WHERE q.question_id = :questionId
        ");
        $stmt->bindParam(':questionId', $questionId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "Question not found.";
        }

        $question = $stmt->fetch();

        // Check if user is authorized to update this question
        if (!isAdmin() && $question['created_by'] != $_SESSION['user_id']) {
            // Check if user is an instructor for this course
            $stmt = $pdo->prepare("
                SELECT ci.instructor_id
                FROM quiz_questions q
                JOIN activities a ON q.activity_id = a.activity_id
                JOIN courses c ON a.course_id = c.course_id
                JOIN course_instructors ci ON c.course_id = ci.course_id
                WHERE q.question_id = :questionId AND ci.instructor_id = :userId
            ");
            $stmt->bindParam(':questionId', $questionId);
            $stmt->bindParam(':userId', $_SESSION['user_id']);
            $stmt->execute();

            if ($stmt->rowCount() == 0) {
                return "You are not authorized to update this question.";
            }
        }

        // Start building the query
        $query = "UPDATE quiz_questions SET question_text = :questionText";
        $params = [
            ':questionText' => $questionText,
            ':questionId' => $questionId
        ];

        // Add optional parameters if provided
        if ($points !== null) {
            $query .= ", points = :points";
            $params[':points'] = $points;
        }

        if ($position !== null) {
            $query .= ", position = :position";
            $params[':position'] = $position;
        }

        $query .= " WHERE question_id = :questionId";

        // Execute the query
        $stmt = $pdo->prepare($query);
        $stmt->execute($params);

        return true;
    } catch (PDOException $e) {
        return "Failed to update question: " . $e->getMessage();
    }
}

/**
 * Function to delete an activity quiz question
 *
 * @param int $questionId The question ID
 * @return bool|string True if deletion successful, error message otherwise
 */
function deleteActivityQuizQuestion($questionId) {
    global $pdo;

    try {
        // Check if the question exists
        $stmt = $pdo->prepare("
            SELECT q.question_id, a.created_by
            FROM quiz_questions q
            JOIN activities a ON q.activity_id = a.activity_id
            WHERE q.question_id = :questionId
        ");
        $stmt->bindParam(':questionId', $questionId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "Question not found.";
        }

        $question = $stmt->fetch();

        // Check if user is authorized to delete this question
        if (!isAdmin() && $question['created_by'] != $_SESSION['user_id']) {
            // Check if user is an instructor for this course
            $stmt = $pdo->prepare("
                SELECT ci.instructor_id
                FROM quiz_questions q
                JOIN activities a ON q.activity_id = a.activity_id
                JOIN courses c ON a.course_id = c.course_id
                JOIN course_instructors ci ON c.course_id = ci.course_id
                WHERE q.question_id = :questionId AND ci.instructor_id = :userId
            ");
            $stmt->bindParam(':questionId', $questionId);
            $stmt->bindParam(':userId', $_SESSION['user_id']);
            $stmt->execute();

            if ($stmt->rowCount() == 0) {
                return "You are not authorized to delete this question.";
            }
        }

        // First delete any options associated with this question
        $stmt = $pdo->prepare("DELETE FROM quiz_options WHERE question_id = :questionId");
        $stmt->bindParam(':questionId', $questionId);
        $stmt->execute();

        // Then delete the question
        $stmt = $pdo->prepare("DELETE FROM quiz_questions WHERE question_id = :questionId");
        $stmt->bindParam(':questionId', $questionId);
        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        return "Failed to delete question: " . $e->getMessage();
    }
}

/**
 * Function to submit an activity (assignment or quiz)
 *
 * @param int $activityId The activity ID
 * @param int $userId The user ID
 * @param string $content The submission content (for assignments)
 * @param array $answers The answers for quiz questions (for quizzes)
 * @param string $filePath The path to the uploaded file (optional)
 * @param string $fileName The name of the uploaded file (optional)
 * @param string $fileType The type of the uploaded file (optional)
 * @param int $fileSize The size of the uploaded file (optional)
 * @return int|string Submission ID if successful, error message otherwise
 */
function submitActivity($activityId, $userId, $content = null, $answers = null, $filePath = null, $fileName = null, $fileType = null, $fileSize = null) {
    global $pdo;

    try {
        // Check if the activity exists
        $stmt = $pdo->prepare("
            SELECT * FROM activities
            WHERE activity_id = :activityId AND is_published = 1
        ");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "Activity not found or not published.";
        }

        $activity = $stmt->fetch();

        // Check if the due date has passed and late submissions are not allowed
        $isLate = false;
        if ($activity['due_date'] !== null) {
            $dueDate = new DateTime($activity['due_date']);
            $now = new DateTime();

            if ($now > $dueDate) {
                $isLate = true;

                // If late submissions are not allowed, return an error
                if (!$activity['allow_late_submissions']) {
                    return "The due date for this activity has passed and late submissions are not allowed.";
                }
            }
        }

        // Check if the user is enrolled in the course
        if (!isEnrolled($userId, $activity['course_id'])) {
            return "You are not enrolled in this course.";
        }

        // Check if a submission already exists
        $stmt = $pdo->prepare("
            SELECT submission_id
            FROM activity_submissions
            WHERE activity_id = :activityId AND user_id = :userId
        ");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->bindParam(':userId', $userId);
        $stmt->execute();

        $pdo->beginTransaction();

        if ($stmt->rowCount() > 0) {
            // Update the existing submission
            $submission = $stmt->fetch();
            $submissionId = $submission['submission_id'];

            // Check if the table has submission_content or content column
            $columnName = 'submission_content';
            try {
                $checkStmt = $pdo->query("SHOW COLUMNS FROM activity_submissions LIKE 'submission_content'");
                if ($checkStmt->rowCount() == 0) {
                    $columnName = 'content';
                }
            } catch (PDOException $e) {
                // If error, default to submission_content
            }

            $stmt = $pdo->prepare("
                UPDATE activity_submissions
                SET $columnName = :content, submission_date = CURRENT_TIMESTAMP,
                    file_path = :filePath, file_name = :fileName, file_type = :fileType, file_size = :fileSize,
                    is_late = :isLate, grade = NULL, feedback = NULL, graded_by = NULL, graded_at = NULL
                WHERE submission_id = :submissionId
            ");
            $stmt->bindParam(':content', $content);
            $stmt->bindParam(':filePath', $filePath);
            $stmt->bindParam(':fileName', $fileName);
            $stmt->bindParam(':fileType', $fileType);
            $stmt->bindParam(':fileSize', $fileSize);
            $stmt->bindParam(':isLate', $isLate, PDO::PARAM_BOOL);
            $stmt->bindParam(':submissionId', $submissionId);
            $stmt->execute();

            // Delete existing answers
            if ($answers !== null) {
                if ($activity['activity_type'] == 'quiz') {
                    // For quizzes, delete from quiz_answers
                    $stmt = $pdo->prepare("DELETE FROM quiz_answers WHERE submission_id = :submissionId");
                    $stmt->bindParam(':submissionId', $submissionId);
                    $stmt->execute();
                    error_log("Deleted existing quiz answers for submission ID: $submissionId");
                } else if ($activity['activity_type'] == 'activity' || $activity['activity_type'] == 'assignment') {
                    // For activities and assignments, delete from activity_answers
                    $stmt = $pdo->prepare("DELETE FROM activity_answers WHERE submission_id = :submissionId");
                    $stmt->bindParam(':submissionId', $submissionId);
                    $stmt->execute();
                    error_log("Deleted existing activity/assignment answers for submission ID: $submissionId");
                }
            }
        } else {
            // Check if the table has submission_content or content column
            $columnName = 'submission_content';
            try {
                $checkStmt = $pdo->query("SHOW COLUMNS FROM activity_submissions LIKE 'submission_content'");
                if ($checkStmt->rowCount() == 0) {
                    $columnName = 'content';
                }
            } catch (PDOException $e) {
                // If error, default to submission_content
            }

            // Create a new submission
            $stmt = $pdo->prepare("
                INSERT INTO activity_submissions (
                    activity_id, user_id, $columnName,
                    file_path, file_name, file_type, file_size,
                    submission_date, is_late
                )
                VALUES (
                    :activityId, :userId, :content,
                    :filePath, :fileName, :fileType, :fileSize,
                    CURRENT_TIMESTAMP, :isLate
                )
            ");
            $stmt->bindParam(':activityId', $activityId);
            $stmt->bindParam(':userId', $userId);
            $stmt->bindParam(':content', $content);
            $stmt->bindParam(':filePath', $filePath);
            $stmt->bindParam(':fileName', $fileName);
            $stmt->bindParam(':fileType', $fileType);
            $stmt->bindParam(':fileSize', $fileSize);
            $stmt->bindParam(':isLate', $isLate, PDO::PARAM_BOOL);
            $stmt->execute();

            $submissionId = $pdo->lastInsertId();
        }

        // If this is a quiz, activity, or assignment with answers, save the answers
        if (($activity['activity_type'] == 'quiz' || $activity['activity_type'] == 'activity' || $activity['activity_type'] == 'assignment') && $answers !== null) {
            // Get all questions for this activity
            $questions = [];
            if ($activity['activity_type'] == 'quiz') {
                $questions = getQuizQuestions($activityId);
            } else {
                // For both activities and assignments, use getActivityQuestions
                error_log("Using getActivityQuestions for " . $activity['activity_type'] . " $activityId in submitActivity");
                $questions = getActivityQuestions($activityId);
            }

            if (is_string($questions)) {
                $pdo->rollBack();
                error_log("Error getting questions: $questions");
                return "Error getting questions: $questions";
            }

            // Debug log the questions and answers
            error_log("Questions for " . $activity['activity_type'] . " $activityId: " . json_encode($questions));
            error_log("Answers provided: " . json_encode($answers));

            // Process each answer
            foreach ($questions as $question) {
                $questionId = $question['question_id'];

                // Skip if no answer provided for this question
                if (!isset($answers[$questionId])) {
                    error_log("No answer provided for question ID: $questionId");
                    continue;
                }

                error_log("Processing answer for question ID: $questionId, Type: " . $question['question_type']);

                $answerText = $answers[$questionId];
                $isCorrect = null;
                $pointsEarned = null;

                // Auto-grade multiple choice and true/false questions
                if ($question['question_type'] == 'multiple_choice' || $question['question_type'] == 'true_false') {
                    // Check if options exist
                    if (!isset($question['options']) || empty($question['options'])) {
                        error_log("Warning: No options found for question ID: $questionId, Type: " . $question['question_type']);
                        // Create default options for true/false questions
                        if ($question['question_type'] == 'true_false') {
                            error_log("Creating default true/false options for question ID: $questionId");
                            $question['options'] = [
                                [
                                    'option_id' => 'true_' . $questionId,
                                    'option_text' => 'True',
                                    'is_correct' => false
                                ],
                                [
                                    'option_id' => 'false_' . $questionId,
                                    'option_text' => 'False',
                                    'is_correct' => false
                                ]
                            ];
                        }
                    }

                    error_log("Answer text for question $questionId: " . $answerText);
                    error_log("Options for question $questionId: " . json_encode($question['options']));

                    // Find the selected option
                    $optionFound = false;
                    foreach ($question['options'] as $option) {
                        error_log("Checking option ID: " . $option['option_id'] . " against answer: " . $answerText);
                        if ($option['option_id'] == $answerText) {
                            $isCorrect = $option['is_correct'] ? 1 : 0;
                            $pointsEarned = $isCorrect ? $question['points'] : 0;
                            $answerText = $option['option_text']; // Store the text of the answer
                            $optionFound = true;
                            error_log("Option found! Is correct: " . ($isCorrect ? 'Yes' : 'No') . ", Points earned: " . $pointsEarned);
                            break;
                        }
                    }

                    if (!$optionFound) {
                        error_log("Warning: No matching option found for question ID: $questionId, Answer: $answerText");
                    }
                } elseif ($question['question_type'] == 'checkbox') {
                    // For checkbox, $answerText is an array of option IDs
                    $selectedOptions = is_array($answerText) ? $answerText : [$answerText];
                    $correctOptions = [];
                    $selectedTexts = [];

                    // Get all correct options
                    foreach ($question['options'] as $option) {
                        if ($option['is_correct']) {
                            $correctOptions[] = $option['option_id'];
                        }

                        // Store the text of selected options
                        if (in_array($option['option_id'], $selectedOptions)) {
                            $selectedTexts[] = $option['option_text'];
                        }
                    }

                    // Check if all selected options are correct and all correct options are selected
                    $allCorrect = true;
                    foreach ($selectedOptions as $optionId) {
                        if (!in_array($optionId, $correctOptions)) {
                            $allCorrect = false;
                            break;
                        }
                    }

                    foreach ($correctOptions as $optionId) {
                        if (!in_array($optionId, $selectedOptions)) {
                            $allCorrect = false;
                            break;
                        }
                    }

                    $isCorrect = $allCorrect ? 1 : 0;
                    $pointsEarned = $allCorrect ? $question['points'] : 0;
                    $answerText = implode(', ', $selectedTexts); // Store the text of the answers
                }

                // Save the answer
                try {
                    if ($activity['activity_type'] == 'activity' || $activity['activity_type'] == 'assignment') {
                        // For activities and assignments, use activity_answers table
                        error_log("Saving answer for " . $activity['activity_type'] . " to activity_answers table. Question ID: $questionId, Answer: $answerText");

                        // Check if the activity_answers table exists
                        try {
                            $tableCheck = $pdo->query("SHOW TABLES LIKE 'activity_answers'");
                            if ($tableCheck->rowCount() == 0) {
                                error_log("Table 'activity_answers' does not exist! Creating it...");

                                // Create the table if it doesn't exist
                                $pdo->exec("
                                    CREATE TABLE IF NOT EXISTS activity_answers (
                                        answer_id INT(11) NOT NULL AUTO_INCREMENT,
                                        submission_id INT(11) NOT NULL,
                                        question_id INT(11) NOT NULL,
                                        answer_text TEXT,
                                        selected_option_id VARCHAR(255),
                                        is_correct TINYINT(1) DEFAULT NULL,
                                        points_earned DECIMAL(10,2) DEFAULT NULL,
                                        PRIMARY KEY (answer_id),
                                        KEY submission_id (submission_id),
                                        KEY question_id (question_id)
                                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                                ");
                                error_log("Table 'activity_answers' created successfully");
                            }
                        } catch (PDOException $tableCheckError) {
                            error_log("Error checking for activity_answers table: " . $tableCheckError->getMessage());
                        }

                        $stmt = $pdo->prepare("
                            INSERT INTO activity_answers (submission_id, question_id, answer_text, selected_option_id, is_correct, points_earned)
                            VALUES (:submissionId, :questionId, :answerText, :selectedOptionId, :isCorrect, :pointsEarned)
                        ");
                        $stmt->bindParam(':submissionId', $submissionId);
                        $stmt->bindParam(':questionId', $questionId);
                        $stmt->bindParam(':answerText', $answerText);
                        $selectedOptionId = isset($selectedOptions[0]) ? $selectedOptions[0] : null;
                        if ($question['question_type'] == 'multiple_choice' || $question['question_type'] == 'true_false') {
                            $selectedOptionId = $answers[$questionId];
                        }
                        $stmt->bindParam(':selectedOptionId', $selectedOptionId);
                        $stmt->bindParam(':isCorrect', $isCorrect);
                        $stmt->bindParam(':pointsEarned', $pointsEarned);

                        error_log("Executing SQL with params: submissionId=$submissionId, questionId=$questionId, answerText=$answerText, selectedOptionId=$selectedOptionId, isCorrect=" . ($isCorrect ? 'true' : 'false') . ", pointsEarned=" . ($pointsEarned ?? 'null'));
                    } else {
                        // For quizzes, use quiz_answers table
                        error_log("Saving answer for quiz to quiz_answers table. Question ID: $questionId, Answer: $answerText");

                        // Check if the quiz_answers table exists
                        try {
                            $tableCheck = $pdo->query("SHOW TABLES LIKE 'quiz_answers'");
                            if ($tableCheck->rowCount() == 0) {
                                error_log("Table 'quiz_answers' does not exist! Creating it...");

                                // Create the table if it doesn't exist
                                $pdo->exec("
                                    CREATE TABLE IF NOT EXISTS quiz_answers (
                                        answer_id INT(11) NOT NULL AUTO_INCREMENT,
                                        submission_id INT(11) NOT NULL,
                                        question_id INT(11) NOT NULL,
                                        answer_text TEXT,
                                        is_correct TINYINT(1) DEFAULT NULL,
                                        points_earned DECIMAL(10,2) DEFAULT NULL,
                                        PRIMARY KEY (answer_id),
                                        KEY submission_id (submission_id),
                                        KEY question_id (question_id)
                                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                                ");
                                error_log("Table 'quiz_answers' created successfully");
                            }
                        } catch (PDOException $tableCheckError) {
                            error_log("Error checking for quiz_answers table: " . $tableCheckError->getMessage());
                        }

                        $stmt = $pdo->prepare("
                            INSERT INTO quiz_answers (submission_id, question_id, answer_text, is_correct, points_earned)
                            VALUES (:submissionId, :questionId, :answerText, :isCorrect, :pointsEarned)
                        ");
                        $stmt->bindParam(':submissionId', $submissionId);
                        $stmt->bindParam(':questionId', $questionId);
                        $stmt->bindParam(':answerText', $answerText);
                        $stmt->bindParam(':isCorrect', $isCorrect);
                        $stmt->bindParam(':pointsEarned', $pointsEarned);

                        error_log("Executing SQL with params: submissionId=$submissionId, questionId=$questionId, answerText=$answerText, isCorrect=" . ($isCorrect ? 'true' : 'false') . ", pointsEarned=" . ($pointsEarned ?? 'null'));
                    }

                    $stmt->execute();
                    error_log("Answer saved successfully for question ID: $questionId");
                } catch (PDOException $e) {
                    error_log("Error saving answer for question ID: $questionId - " . $e->getMessage());
                    // Continue processing other answers even if one fails
                }
            }

            // Auto-grade the activity if all questions are auto-gradable
            $allAutoGraded = true;
            $totalPoints = 0;
            $earnedPoints = 0;
            $answeredQuestions = 0;

            foreach ($questions as $question) {
                // Skip questions that weren't answered
                if (!isset($answers[$question['question_id']])) {
                    continue;
                }

                $answeredQuestions++;

                if ($question['question_type'] != 'multiple_choice' && $question['question_type'] != 'true_false' && $question['question_type'] != 'checkbox') {
                    $allAutoGraded = false;
                    break;
                }

                $totalPoints += $question['points'];

                // Find the answer for this question
                if ($activity['activity_type'] == 'activity' || $activity['activity_type'] == 'assignment') {
                    $stmt = $pdo->prepare("
                        SELECT points_earned
                        FROM activity_answers
                        WHERE submission_id = :submissionId AND question_id = :questionId
                    ");
                } else {
                    $stmt = $pdo->prepare("
                        SELECT points_earned
                        FROM quiz_answers
                        WHERE submission_id = :submissionId AND question_id = :questionId
                    ");
                }
                $stmt->bindParam(':submissionId', $submissionId);
                $stmt->bindParam(':questionId', $question['question_id']);
                $stmt->execute();

                if ($stmt->rowCount() > 0) {
                    $answer = $stmt->fetch();
                    $earnedPoints += $answer['points_earned'] !== null ? $answer['points_earned'] : 0;
                }
            }

            // Always calculate a grade if there are questions, even for assignments
            // For assignments, we'll always calculate a grade even if there are no auto-gradable questions
            if ($activity['activity_type'] == 'assignment' || ($answeredQuestions > 0 && $totalPoints > 0)) {
                // For assignments without auto-gradable questions, give full points
                if ($activity['activity_type'] == 'assignment' && ($answeredQuestions == 0 || $totalPoints == 0)) {
                    // Set grade to 100% for assignments without auto-gradable questions
                    $grade = 100;
                    error_log("Assignment without auto-gradable questions, setting grade to 100%");
                } else {
                    // Calculate the grade as a percentage
                    $grade = ($earnedPoints / $totalPoints) * 100;
                }

                error_log("Auto-grading submission $submissionId: Earned $earnedPoints out of $totalPoints points, grade: $grade%");

                // Update the submission with the auto-graded score
                $stmt = $pdo->prepare("
                    UPDATE activity_submissions
                    SET grade = :grade, graded_at = CURRENT_TIMESTAMP
                    WHERE submission_id = :submissionId
                ");
                $stmt->bindParam(':grade', $grade);
                $stmt->bindParam(':submissionId', $submissionId);
                $stmt->execute();

                error_log("Updated submission $submissionId with grade: $grade%");
            } else {
                error_log("Not auto-grading submission $submissionId: answeredQuestions=$answeredQuestions, totalPoints=$totalPoints, allAutoGraded=" . ($allAutoGraded ? 'true' : 'false'));
            }
        }

        $pdo->commit();
        return $submissionId;
    } catch (PDOException $e) {
        $pdo->rollBack();
        return "Failed to submit activity: " . $e->getMessage();
    }
}

/**
 * Function to grade an activity submission
 * Admin can grade any submission, teachers can only grade submissions for their own courses
 *
 * @param int $submissionId The submission ID
 * @param float $grade The grade
 * @param string $feedback The feedback
 * @return bool|string True if grading successful, error message otherwise
 */
function gradeActivitySubmission($submissionId, $grade, $feedback = null) {
    global $pdo;

    try {
        // Check if user is authorized to grade this submission
        if (!isAdmin() && !isTeacher()) {
            return "You are not authorized to grade submissions.";
        }

        // Get the submission details
        $stmt = $pdo->prepare("
            SELECT s.submission_id, s.activity_id, s.user_id, a.course_id, a.created_by
            FROM activity_submissions s
            JOIN activities a ON s.activity_id = a.activity_id
            WHERE s.submission_id = :submissionId
        ");
        $stmt->bindParam(':submissionId', $submissionId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "Submission not found.";
        }

        $submission = $stmt->fetch();

        // Check if user is authorized to grade this submission
        if (!isAdmin()) {
            // Check if the user is the course creator or an instructor
            $stmt = $pdo->prepare("
                SELECT c.course_id
                FROM courses c
                LEFT JOIN course_instructors ci ON c.course_id = ci.course_id
                WHERE c.course_id = :courseId AND (c.created_by = :userId OR ci.instructor_id = :instructorId)
            ");
            $stmt->bindParam(':courseId', $submission['course_id']);
            $stmt->bindParam(':userId', $_SESSION['user_id']);
            $stmt->bindParam(':instructorId', $_SESSION['user_id']);
            $stmt->execute();

            if ($stmt->rowCount() == 0) {
                return "You are not authorized to grade submissions for this course.";
            }
        }

        // Grade the submission
        $stmt = $pdo->prepare("
            UPDATE activity_submissions
            SET grade = :grade, feedback = :feedback, graded_by = :gradedBy, graded_at = CURRENT_TIMESTAMP
            WHERE submission_id = :submissionId
        ");

        $stmt->bindParam(':grade', $grade);
        $stmt->bindParam(':feedback', $feedback);
        $stmt->bindParam(':gradedBy', $_SESSION['user_id']);
        $stmt->bindParam(':submissionId', $submissionId);
        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        return "Failed to grade submission: " . $e->getMessage();
    }
}

/**
 * Function to get all submissions for an activity
 * Admin can view any submissions, teachers can only view submissions for their own courses
 *
 * @param int $activityId The activity ID
 * @return array|string Array of submissions if successful, error message otherwise
 */
function getSubmissionsByActivity($activityId) {
    global $pdo;

    try {
        // Check if the activity exists
        $stmt = $pdo->prepare("
            SELECT a.activity_id, a.course_id, a.created_by
            FROM activities a
            WHERE a.activity_id = :activityId
        ");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "Activity not found.";
        }

        $activity = $stmt->fetch();

        // Check if user is authorized to view these submissions
        if (!isAdmin() && !isTeacher()) {
            return "You are not authorized to view these submissions.";
        }

        if (!isAdmin()) {
            // Check if the user is the course creator or an instructor
            $stmt = $pdo->prepare("
                SELECT c.course_id
                FROM courses c
                LEFT JOIN course_instructors ci ON c.course_id = ci.course_id
                WHERE c.course_id = :courseId AND (c.created_by = :userId OR ci.instructor_id = :instructorId)
            ");
            $stmt->bindParam(':courseId', $activity['course_id']);
            $stmt->bindParam(':userId', $_SESSION['user_id']);
            $stmt->bindParam(':instructorId', $_SESSION['user_id']);
            $stmt->execute();

            if ($stmt->rowCount() == 0) {
                return "You are not authorized to view submissions for this course.";
            }
        }

        // Get the submissions
        $stmt = $pdo->prepare("
            SELECT s.submission_id, s.user_id, u.username, u.first_name, u.last_name,
                   s.submission_content, s.submission_date, s.grade, s.feedback, s.graded_at,
                   g.username as grader_name
            FROM activity_submissions s
            JOIN users u ON s.user_id = u.user_id
            LEFT JOIN users g ON s.graded_by = g.user_id
            WHERE s.activity_id = :activityId
            ORDER BY s.submission_date DESC
        ");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve submissions: " . $e->getMessage();
    }
}

/**
 * Function to get a submission by ID
 * Admin can view any submission, teachers can only view submissions for their own courses, students can only view their own submissions
 *
 * @param int $submissionId The submission ID
 * @return array|string Submission data if successful, error message otherwise
 */
function getActivitySubmissionById($submissionId) {
    global $pdo;

    try {
        // Get the submission details
        $stmt = $pdo->prepare("
            SELECT s.submission_id, s.activity_id, s.user_id, s.submission_content,
                   s.submission_date, s.grade, s.feedback, s.graded_at, s.graded_by,
                   a.course_id, a.title as activity_title, a.activity_type,
                   u.username, u.first_name, u.last_name,
                   g.username as grader_name
            FROM activity_submissions s
            JOIN activities a ON s.activity_id = a.activity_id
            JOIN users u ON s.user_id = u.user_id
            LEFT JOIN users g ON s.graded_by = g.user_id
            WHERE s.submission_id = :submissionId
        ");
        $stmt->bindParam(':submissionId', $submissionId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "Submission not found.";
        }

        $submission = $stmt->fetch();

        // Check if user is authorized to view this submission
        if (!isAdmin()) {
            if (isTeacher()) {
                // Check if the user is the course creator or an instructor
                $stmt = $pdo->prepare("
                    SELECT c.course_id
                    FROM courses c
                    LEFT JOIN course_instructors ci ON c.course_id = ci.course_id
                    WHERE c.course_id = :courseId AND (c.created_by = :userId OR ci.instructor_id = :instructorId)
                ");
                $stmt->bindParam(':courseId', $submission['course_id']);
                $stmt->bindParam(':userId', $_SESSION['user_id']);
                $stmt->bindParam(':instructorId', $_SESSION['user_id']);
                $stmt->execute();

                if ($stmt->rowCount() == 0) {
                    return "You are not authorized to view this submission.";
                }
            } else {
                // Students can only view their own submissions
                if ($submission['user_id'] != $_SESSION['user_id']) {
                    return "You are not authorized to view this submission.";
                }
            }
        }

        // If this is a quiz, activity, or assignment submission, get the answers
        if ($submission['activity_type'] == 'quiz') {
            $stmt = $pdo->prepare("
                SELECT a.answer_id, a.question_id, a.answer_text, a.is_correct, a.points_earned,
                       q.question_text, q.question_type, q.points
                FROM quiz_answers a
                JOIN quiz_questions q ON a.question_id = q.question_id
                WHERE a.submission_id = :submissionId
                ORDER BY q.position, q.question_id
            ");
            $stmt->bindParam(':submissionId', $submissionId);
            $stmt->execute();

            $submission['answers'] = $stmt->fetchAll();
        } elseif ($submission['activity_type'] == 'activity' || $submission['activity_type'] == 'assignment') {
            // Try to get answers from activity_questions first
            $stmt = $pdo->prepare("
                SELECT a.answer_id, a.question_id, a.answer_text, a.selected_option_id, a.is_correct, a.points_earned,
                       q.question_text, q.question_type, q.points
                FROM activity_answers a
                JOIN activity_questions q ON a.question_id = q.question_id
                WHERE a.submission_id = :submissionId
                ORDER BY q.position, q.question_id
            ");
            $stmt->bindParam(':submissionId', $submissionId);
            $stmt->execute();

            $answers = $stmt->fetchAll();

            // If no answers found, try quiz_questions as a fallback
            if (empty($answers)) {
                error_log("No answers found in activity_questions for submission $submissionId, trying quiz_questions");
                $stmt = $pdo->prepare("
                    SELECT a.answer_id, a.question_id, a.answer_text, a.selected_option_id, a.is_correct, a.points_earned,
                           q.question_text, q.question_type, q.points
                    FROM activity_answers a
                    JOIN quiz_questions q ON a.question_id = q.question_id
                    WHERE a.submission_id = :submissionId
                    ORDER BY q.position, q.question_id
                ");
                $stmt->bindParam(':submissionId', $submissionId);
                $stmt->execute();

                $answers = $stmt->fetchAll();
            }

            $submission['answers'] = $answers;
        }

        return $submission;
    } catch (PDOException $e) {
        return "Failed to retrieve submission: " . $e->getMessage();
    }
}

/**
 * Function to get files attached to an activity
 *
 * @param int $activityId The activity ID
 * @return array|string Array of files if successful, error message otherwise
 */
function getActivityFiles($activityId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT *
            FROM activity_files
            WHERE activity_id = :activityId
        ");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve activity files: " . $e->getMessage();
    }
}

/**
 * Function to get a student's submission for an activity
 *
 * @param int $activityId The activity ID
 * @param int $userId The user ID
 * @return array|string Submission data if successful, error message otherwise
 */
function getStudentActivitySubmission($activityId, $userId) {
    global $pdo;

    try {
        // Check if a submission exists
        $stmt = $pdo->prepare("
            SELECT submission_id
            FROM activity_submissions
            WHERE activity_id = :activityId AND user_id = :userId
        ");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->bindParam(':userId', $userId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "No submission found.";
        }

        $submission = $stmt->fetch();

        // Get the full submission details
        return getActivitySubmissionById($submission['submission_id']);
    } catch (PDOException $e) {
        return "Failed to retrieve submission: " . $e->getMessage();
    }
}

/**
 * Function to associate an activity with a module
 *
 * @param int $activityId The activity ID
 * @param int $moduleId The module ID
 * @return bool|string True if association successful, error message otherwise
 */
function associateActivityWithModule($activityId, $moduleId) {
    global $pdo;

    try {
        // Check if the activity exists
        $stmt = $pdo->prepare("SELECT activity_id, course_id FROM activities WHERE activity_id = :activityId");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "Activity not found.";
        }

        $activity = $stmt->fetch();

        // Check if the module exists and belongs to the same course
        $stmt = $pdo->prepare("SELECT module_id, course_id FROM modules WHERE module_id = :moduleId");
        $stmt->bindParam(':moduleId', $moduleId);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return "Module not found.";
        }

        $module = $stmt->fetch();

        if ($activity['course_id'] != $module['course_id']) {
            return "The module does not belong to the same course as the activity.";
        }

        // Check if the activity_modules table exists
        $stmt = $pdo->query("SHOW TABLES LIKE 'activity_modules'");
        if ($stmt->rowCount() == 0) {
            // Create the table if it doesn't exist
            $pdo->exec("
                CREATE TABLE activity_modules (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    activity_id INT NOT NULL,
                    module_id INT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (activity_id) REFERENCES activities(activity_id) ON DELETE CASCADE,
                    FOREIGN KEY (module_id) REFERENCES modules(module_id) ON DELETE CASCADE,
                    UNIQUE KEY unique_activity_module (activity_id, module_id)
                )
            ");
        }

        // Check if the association already exists
        $stmt = $pdo->prepare("
            SELECT id FROM activity_modules
            WHERE activity_id = :activityId AND module_id = :moduleId
        ");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->bindParam(':moduleId', $moduleId);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            // Association already exists
            return true;
        }

        // Create the association
        $stmt = $pdo->prepare("
            INSERT INTO activity_modules (activity_id, module_id)
            VALUES (:activityId, :moduleId)
        ");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->bindParam(':moduleId', $moduleId);
        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        return "Failed to associate activity with module: " . $e->getMessage();
    }
}



/**
 * Function to get all options for a question
 *
 * @param int $questionId The question ID
 * @return array|string Array of options if successful, error message otherwise
 */
function getQuestionOptions($questionId) {
    global $pdo;

    try {
        // First try to get options from quiz_options table
        $stmt = $pdo->prepare("
            SELECT option_id, option_text, is_correct
            FROM quiz_options
            WHERE question_id = :questionId
            ORDER BY position, option_id
        ");
        $stmt->bindParam(':questionId', $questionId);
        $stmt->execute();

        $options = $stmt->fetchAll();

        // If no options found in quiz_options, try activity_question_options
        if (count($options) == 0) {
            $stmt = $pdo->prepare("
                SELECT option_id, option_text, is_correct
                FROM activity_question_options
                WHERE question_id = :questionId
                ORDER BY position, option_id
            ");
            $stmt->bindParam(':questionId', $questionId);
            $stmt->execute();

            $options = $stmt->fetchAll();
        }

        return $options;
    } catch (PDOException $e) {
        return "Failed to retrieve options: " . $e->getMessage();
    }
}

/**
 * Function to check if a student has completed an activity
 *
 * @param int $activityId The activity ID
 * @param int $userId The user ID
 * @return bool True if the activity is completed, false otherwise
 */
function isActivityCompleted($activityId, $userId) {
    global $pdo;

    try {
        // Check if a submission exists
        $stmt = $pdo->prepare("
            SELECT submission_id
            FROM activity_submissions
            WHERE activity_id = :activityId AND user_id = :userId
        ");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->bindParam(':userId', $userId);
        $stmt->execute();

        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        error_log("Failed to check if activity is completed: " . $e->getMessage());
        return false;
    }
}

/**
 * Function to get all submissions for an activity from the activity_submissions table
 *
 * @param int $activityId The activity ID
 * @return array|string Array of submissions if successful, error message otherwise
 */
function getActivitySubmissionsFromTable($activityId) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT s.*, u.first_name, u.last_name, u.username
            FROM activity_submissions s
            JOIN users u ON s.user_id = u.user_id
            WHERE s.activity_id = :activityId
            ORDER BY s.submission_date DESC
        ");
        $stmt->bindParam(':activityId', $activityId);
        $stmt->execute();

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return "Failed to retrieve submissions: " . $e->getMessage();
    }
}
