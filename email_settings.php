<?php
// Include configuration file
require_once 'includes/config.php';
require_once 'includes/auth.php';

// Check if user is logged in and is an admin
if (!isLoggedIn() || $_SESSION['role_id'] != 1) {
    header("location: login.php");
    exit;
}

// Initialize variables
$success_msg = "";
$error_msg = "";

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Get the form data
    $smtpHost = trim($_POST["smtp_host"]);
    $smtpPort = trim($_POST["smtp_port"]);
    $smtpSecure = trim($_POST["smtp_secure"]);
    $smtpAuth = isset($_POST["smtp_auth"]) ? 'true' : 'false';
    $smtpUsername = trim($_POST["smtp_username"]);
    $smtpPassword = trim($_POST["smtp_password"]);
    $mailFromEmail = trim($_POST["mail_from_email"]);
    $mailFromName = trim($_POST["mail_from_name"]);
    $mailDebug = trim($_POST["mail_debug"]);
    
    // Validate the form data
    $errors = false;
    
    if (empty($smtpHost)) {
        $error_msg = "SMTP Host is required.";
        $errors = true;
    }
    
    if (empty($smtpPort)) {
        $error_msg = "SMTP Port is required.";
        $errors = true;
    } elseif (!is_numeric($smtpPort)) {
        $error_msg = "SMTP Port must be a number.";
        $errors = true;
    }
    
    if (empty($mailFromEmail)) {
        $error_msg = "From Email is required.";
        $errors = true;
    } elseif (!filter_var($mailFromEmail, FILTER_VALIDATE_EMAIL)) {
        $error_msg = "From Email must be a valid email address.";
        $errors = true;
    }
    
    if (empty($mailFromName)) {
        $error_msg = "From Name is required.";
        $errors = true;
    }
    
    // If no errors, update the configuration file
    if (!$errors) {
        $configFile = __DIR__ . '/includes/mailer_config.php';
        $configContent = file_get_contents($configFile);
        
        // Update the configuration values
        $configContent = preg_replace("/define\('SMTP_HOST', '.*?'\);/", "define('SMTP_HOST', '$smtpHost');", $configContent);
        $configContent = preg_replace("/define\('SMTP_PORT', \d+\);/", "define('SMTP_PORT', $smtpPort);", $configContent);
        $configContent = preg_replace("/define\('SMTP_SECURE', '.*?'\);/", "define('SMTP_SECURE', '$smtpSecure');", $configContent);
        $configContent = preg_replace("/define\('SMTP_AUTH', (?:true|false)\);/", "define('SMTP_AUTH', $smtpAuth);", $configContent);
        $configContent = preg_replace("/define\('SMTP_USERNAME', '.*?'\);/", "define('SMTP_USERNAME', '$smtpUsername');", $configContent);
        $configContent = preg_replace("/define\('SMTP_PASSWORD', '.*?'\);/", "define('SMTP_PASSWORD', '$smtpPassword');", $configContent);
        $configContent = preg_replace("/define\('MAIL_FROM_EMAIL', .*?\);/", "define('MAIL_FROM_EMAIL', '$mailFromEmail');", $configContent);
        $configContent = preg_replace("/define\('MAIL_FROM_NAME', .*?\);/", "define('MAIL_FROM_NAME', '$mailFromName');", $configContent);
        $configContent = preg_replace("/define\('MAIL_DEBUG', \d+\);/", "define('MAIL_DEBUG', $mailDebug);", $configContent);
        
        // Write the updated configuration to the file
        if (file_put_contents($configFile, $configContent)) {
            $success_msg = "Email settings updated successfully.";
        } else {
            $error_msg = "Failed to update email settings. Please check file permissions.";
        }
    }
}

// Get the current configuration values
$configFile = __DIR__ . '/includes/mailer_config.php';
$configContent = file_get_contents($configFile);

// Extract the configuration values
preg_match("/define\('SMTP_HOST', '(.*?)'\);/", $configContent, $matches);
$smtpHost = $matches[1] ?? '';

preg_match("/define\('SMTP_PORT', (\d+)\);/", $configContent, $matches);
$smtpPort = $matches[1] ?? '';

preg_match("/define\('SMTP_SECURE', '(.*?)'\);/", $configContent, $matches);
$smtpSecure = $matches[1] ?? '';

preg_match("/define\('SMTP_AUTH', (true|false)\);/", $configContent, $matches);
$smtpAuth = ($matches[1] ?? 'false') === 'true';

preg_match("/define\('SMTP_USERNAME', '(.*?)'\);/", $configContent, $matches);
$smtpUsername = $matches[1] ?? '';

preg_match("/define\('SMTP_PASSWORD', '(.*?)'\);/", $configContent, $matches);
$smtpPassword = $matches[1] ?? '';

preg_match("/define\('MAIL_FROM_EMAIL', (.*?)\);/", $configContent, $matches);
$mailFromEmail = str_replace("'", "", $matches[1] ?? APP_EMAIL);

preg_match("/define\('MAIL_FROM_NAME', (.*?)\);/", $configContent, $matches);
$mailFromName = str_replace("'", "", $matches[1] ?? APP_NAME);

preg_match("/define\('MAIL_DEBUG', (\d+)\);/", $configContent, $matches);
$mailDebug = $matches[1] ?? '0';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Settings - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Google+Sans:wght@400;500;700&family=Roboto:wght@300;400;500;700&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="css/light-theme.css">
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            padding: 20px;
        }
        .card {
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            padding: 15px 20px;
        }
        .card-body {
            padding: 20px;
        }
        .form-group label {
            font-weight: 500;
        }
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        .btn-primary:hover {
            background-color: var(--primary-hover);
            border-color: var(--primary-hover);
        }
        .back-link {
            margin-bottom: 20px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="admin_dashboard.php" class="back-link"><i class="fas fa-arrow-left"></i> Back to Dashboard</a>
        
        <h1><i class="fas fa-envelope"></i> Email Settings</h1>
        <p class="text-muted">Configure the email settings for your application.</p>
        
        <?php if (!empty($error_msg)): ?>
            <div class="alert alert-danger"><?php echo $error_msg; ?></div>
        <?php endif; ?>
        
        <?php if (!empty($success_msg)): ?>
            <div class="alert alert-success"><?php echo $success_msg; ?></div>
        <?php endif; ?>
        
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-cog"></i> SMTP Configuration</h5>
            </div>
            <div class="card-body">
                <form method="post" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="smtp_host">SMTP Host</label>
                                <input type="text" name="smtp_host" id="smtp_host" class="form-control" value="<?php echo htmlspecialchars($smtpHost); ?>" required>
                                <small class="form-text text-muted">e.g., smtp.gmail.com, smtp.office365.com</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="smtp_port">SMTP Port</label>
                                <input type="number" name="smtp_port" id="smtp_port" class="form-control" value="<?php echo htmlspecialchars($smtpPort); ?>" required>
                                <small class="form-text text-muted">Common ports: 587 (TLS), 465 (SSL)</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="smtp_secure">Security</label>
                                <select name="smtp_secure" id="smtp_secure" class="form-control">
                                    <option value="tls" <?php echo $smtpSecure === 'tls' ? 'selected' : ''; ?>>TLS</option>
                                    <option value="ssl" <?php echo $smtpSecure === 'ssl' ? 'selected' : ''; ?>>SSL</option>
                                    <option value="" <?php echo $smtpSecure === '' ? 'selected' : ''; ?>>None</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="smtp_auth">Authentication</label>
                                <div class="custom-control custom-switch mt-2">
                                    <input type="checkbox" class="custom-control-input" id="smtp_auth" name="smtp_auth" <?php echo $smtpAuth ? 'checked' : ''; ?>>
                                    <label class="custom-control-label" for="smtp_auth">Enable SMTP Authentication</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="smtp_username">SMTP Username</label>
                                <input type="text" name="smtp_username" id="smtp_username" class="form-control" value="<?php echo htmlspecialchars($smtpUsername); ?>">
                                <small class="form-text text-muted">Usually your email address</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="smtp_password">SMTP Password</label>
                                <input type="password" name="smtp_password" id="smtp_password" class="form-control" value="<?php echo htmlspecialchars($smtpPassword); ?>">
                                <small class="form-text text-muted">For Gmail, use an App Password if 2FA is enabled</small>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="mail_from_email">From Email</label>
                                <input type="email" name="mail_from_email" id="mail_from_email" class="form-control" value="<?php echo htmlspecialchars($mailFromEmail); ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="mail_from_name">From Name</label>
                                <input type="text" name="mail_from_name" id="mail_from_name" class="form-control" value="<?php echo htmlspecialchars($mailFromName); ?>" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="mail_debug">Debug Level</label>
                                <select name="mail_debug" id="mail_debug" class="form-control">
                                    <option value="0" <?php echo $mailDebug === '0' ? 'selected' : ''; ?>>Off</option>
                                    <option value="1" <?php echo $mailDebug === '1' ? 'selected' : ''; ?>>Client Messages</option>
                                    <option value="2" <?php echo $mailDebug === '2' ? 'selected' : ''; ?>>Client & Server Messages</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group mt-4">
                        <button type="submit" class="btn btn-primary"><i class="fas fa-save"></i> Save Settings</button>
                        <a href="test_email.php" class="btn btn-secondary ml-2"><i class="fas fa-paper-plane"></i> Test Email</a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</body>
</html>
